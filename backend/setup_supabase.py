#!/usr/bin/env python3

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_storage_bucket():
    """Create the hsa-documents storage bucket"""
    url = os.getenv("SUPABASE_URL")
    service_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not service_key:
        print("❌ Missing Supabase configuration!")
        return False
    
    # Create storage bucket
    endpoint = f"{url}/storage/v1/bucket"
    headers = {
        "apikey": service_key,
        "Authorization": f"Bearer {service_key}",
        "Content-Type": "application/json"
    }
    
    bucket_data = {
        "id": "hsa-documents",
        "name": "hsa-documents",
        "public": True,
        "file_size_limit": 52428800,  # 50MB
        "allowed_mime_types": ["image/jpeg", "image/png", "image/gif", "application/pdf", "image/webp"]
    }
    
    try:
        response = requests.post(endpoint, headers=headers, json=bucket_data)
        print(f"Create bucket response: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ hsa-documents storage bucket created successfully")
            return True
        elif "already exists" in response.text.lower():
            print("✅ hsa-documents storage bucket already exists")
            return True
        else:
            print(f"❌ Error creating storage bucket: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error creating bucket: {str(e)}")
        return False

def setup_bucket_policies():
    """Set up RLS policies for the storage bucket"""
    url = os.getenv("SUPABASE_URL")
    service_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    # This would typically be done through the Supabase dashboard or SQL
    # For now, we'll just create the bucket and rely on the default policies
    print("📝 Note: You may need to set up Row Level Security policies in Supabase dashboard")
    print("   Go to Storage > hsa-documents > Policies")
    print("   Add policies for authenticated users to upload/read their own files")

if __name__ == "__main__":
    print("Setting up Supabase resources...")
    
    if create_storage_bucket():
        setup_bucket_policies()
        print("\n✅ Supabase setup completed!")
    else:
        print("\n❌ Supabase setup failed!")
