import AsyncStorage from '@react-native-async-storage/async-storage';
import config from '../config/config';
import apiClient from './apiClient';

// Consistent storage keys
const TOKEN_KEY = config.AUTH.TOKEN_STORAGE_KEY || 'auth_token';

const API_BASE_URL = config.API_BASE_URL;

class TransactionService {
  async getHSASummary(year = null) {
    try {
      // Add year as a query parameter if provided
      const endpoint = year ? 
        `${API_BASE_URL}/hsa/summary?year=${year}` : 
        `${API_BASE_URL}/hsa/summary`;
      
      const response = await apiClient.get(endpoint);

      if (response.status === 200) {
        return response.data;
      } else {
        console.log('Failed to fetch HSA summary:', response.status);
        return this.getDefaultHSASummary();
      }
    } catch (error) {
      console.error('Error fetching HSA summary:', error);
      
      // Return default data instead of throwing error
      return this.getDefaultHSASummary();
    }
  }
  
  // Helper method to get default HSA summary data
  getDefaultHSASummary() {
    return {
      totalExpenses: 0,
      total_expenses: 0,
      maxContribution: 0,
      max_contribution: 0,
      yearlyContribution: 0,
      yearlyExpenses: 0
    };
  }

  async getRecentTransactions() {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/transactions/recent`);

      if (response.status === 200) {
        return response.data;
      } else {
        console.log('Failed to fetch recent transactions:', response.status);
        return {
          monthly_transactions: [],
          recent_items: []
        };
      }
    } catch (error) {
      console.error('Error fetching recent transactions:', error);
      return {
        monthly_transactions: [],
        recent_items: []
      };
    }
  }

  async getCurrentYearContributions() {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/contributions/current-year`);

      if (response.status === 200) {
        return response.data;
      } else {
        console.log('Failed to fetch contributions:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching contributions:', error);
      return [];
    }
  }

  async getThisMonthsTransactions() {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/documents/monthly`);

      if (response.status === 200) {
        const data = response.data;
        return {
          transactions: data.documents || [],
          month: data.month,
          totalCount: data.total_count || 0
        };
      } else {
        console.log('Failed to fetch monthly transactions:', response.status);
        return {
          transactions: [],
          month: '',
          totalCount: 0
        };
      }
    } catch (error) {
      console.error('Error fetching monthly transactions:', error);
      // Return empty array instead of throwing error
      return {
        transactions: [],
        month: '',
        totalCount: 0
      };
    }
  }

  async getAllTransactions() {
    try {
      const allDocuments = await this.getAllHSADocuments();
      return {
        success: true,
        transactions: allDocuments.map(doc => this.formatTransaction(doc))
      };
    } catch (error) {
      console.error('Error fetching all transactions:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getAllHSADocuments() {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/documents`);

      if (response.status === 200) {
        return response.data.documents || [];
      } else {
        console.log('Failed to fetch HSA documents:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching HSA documents:', error);
      // Return empty array instead of throwing error
      return [];
    }
  }

  async getTransactions(month, year) {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/transactions?month=${month}&year=${year}`);

      if (response.status === 200) {
        return response.data;
      } else {
        console.log('Failed to fetch transactions:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching monthly transactions:', error);
      // Return empty array instead of throwing error
      return [];
    }
  }

  // Helper method to calculate total amount for the month
  calculateMonthlyTotal(transactions) {
    return transactions.reduce((total, transaction) => {
      return total + (parseFloat(transaction.amount) || 0);
    }, 0);
  }

  // Helper method to format transaction for display
  formatTransaction(transaction) {
    return {
      id: transaction.id,
      title: transaction.title || transaction.file_name || '',
      vendor: transaction.vendor || transaction.title || transaction.file_name || 'Unknown Vendor',
      amount: parseFloat(transaction.amount) || 0,
      category: transaction.category || 'Medical',
      date: transaction.expense_date,
      status: transaction.status || 'pending',
      description: transaction.description
    };
  }

  // Helper method to group transactions by category
  groupTransactionsByCategory(transactions) {
    return transactions.reduce((groups, transaction) => {
      const category = transaction.category || 'Medical';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(transaction);
      return groups;
    }, {});
  }

  // Get spending data by month for a specified year
  async getYearlySpendingData(year) {
    try {
      const allDocuments = await this.getAllHSADocuments();
      
      // Filter documents for the specified year
      const yearDocuments = allDocuments.filter(doc => {
        const expenseDate = new Date(doc.expense_date);
        return expenseDate.getFullYear() === parseInt(year);
      });
      
      // Initialize monthly data with zeros for all months
      const monthlyData = Array(12).fill(0);
      
      // Sum up amounts by month
      yearDocuments.forEach(doc => {
        const expenseDate = new Date(doc.expense_date);
        const month = expenseDate.getMonth(); // 0-indexed month
        monthlyData[month] += parseFloat(doc.amount) || 0;
      });
      
      // Calculate total for the year
      const yearlyTotal = monthlyData.reduce((sum, amount) => sum + amount, 0);
      
      return {
        data: monthlyData,
        total: yearlyTotal
      };
    } catch (error) {
      console.error('Error generating yearly spending data:', error);
      throw error;
    }
  }

  // Get spending data by category for a specified year
  async getCategorySpendingData(year) {
    try {
      const allDocuments = await this.getAllHSADocuments();
      
      // Filter documents for the specified year
      const yearDocuments = allDocuments.filter(doc => {
        const expenseDate = new Date(doc.expense_date);
        return expenseDate.getFullYear() === parseInt(year);
      });
      
      // Group documents by category and sum amounts
      const categoryMap = {};
      
      yearDocuments.forEach(doc => {
        const category = doc.category || 'Medical';
        if (!categoryMap[category]) {
          categoryMap[category] = 0;
        }
        categoryMap[category] += parseFloat(doc.amount) || 0;
      });
      
      // Convert to the format needed for charts
      const categoryData = Object.keys(categoryMap).map(name => ({
        name,
        amount: categoryMap[name]
      }));
      
      return categoryData;
    } catch (error) {
      console.error('Error generating category spending data:', error);
      throw error;
    }
  }

  // Get available years from transaction history
  async getAvailableYears() {
    try {
      const allDocuments = await this.getAllHSADocuments();
      
      // Extract unique years from documents
      const yearsSet = new Set();
      allDocuments.forEach(doc => {
        if (doc.expense_date) {
          const year = new Date(doc.expense_date).getFullYear();
          yearsSet.add(year.toString());
        }
      });
      
      // Convert to array and sort in descending order
      const years = Array.from(yearsSet).sort((a, b) => b - a);
      
      // Add current year if not in list
      const currentYear = new Date().getFullYear().toString();
      if (!years.includes(currentYear)) {
        years.unshift(currentYear);
      }
      
      return years;
    } catch (error) {
      console.error('Error getting available years:', error);
      throw error;
    }
  }

  // Get monthly chart data for expenses and contributions
  async getMonthlyChartData(type = null) {
    try {
      console.log('Fetching monthly chart data for type:', type);
      
      // We don't need to manually set the token here anymore
      // The axios interceptor in AuthContext should handle this automatically
      const response = await apiClient.get(`${API_BASE_URL}/hsa/monthly-chart-data`);

      if (response.status === 200) {
        const data = response.data;
        console.log('Monthly chart data received:', data.length, 'months');
        
        // Debug: Log the first month's data to check structure
        if (data.length > 0) {
          console.log('First month data sample:', data[0]);
          console.log('Contributions present in data:', data.some(item => (item.contributions || 0) > 0));
          console.log('Expenses present in data:', data.some(item => (item.expenses || 0) > 0));
          
          // Log all months with contributions or expenses
          if (type === 'Contribution') {
            const monthsWithContributions = data.filter(item => (item.contributions || 0) > 0);
            console.log('Months with contributions:', monthsWithContributions.length);
            monthsWithContributions.forEach(item => {
              console.log(`${item.month}: $${item.contributions || 0}`);
            });
          } else if (type === 'Expense') {
            const monthsWithExpenses = data.filter(item => (item.expenses || 0) > 0);
            console.log('Months with expenses:', monthsWithExpenses.length);
            monthsWithExpenses.forEach(item => {
              console.log(`${item.month}: $${item.expenses || 0}`);
            });
          }
        }
        
        return data;
      } else {
        console.log('Failed to fetch monthly chart data:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching monthly chart data:', error);
      // Simply return an empty array instead of throwing error
      return [];
    }
  }

  // Get weekly chart data for expenses or contributions
  async getWeeklyChartData(startDate, endDate, type) {
    try {
      // Convert type to match backend expectation
      const transactionType = type === 'Expense' ? 'expense' : 'contribution';
      
      const response = await apiClient.get(`${API_BASE_URL}/hsa/weekly-chart-data?start=${startDate}&end=${endDate}&type=${transactionType}`);

      if (response.status === 200) {
        return response.data;
      } else {
        console.log('Failed to fetch weekly chart data:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching weekly chart data:', error);
      return [];
    }
  }
  
  // Get yearly chart data by month for expenses or contributions
  async getYearlyChartData(year, type) {
    try {
      // Convert type to match backend expectation
      const transactionType = type === 'Expense' ? 'expense' : 'contribution';
      
      const response = await apiClient.get(`${API_BASE_URL}/hsa/yearly-chart-data?year=${year}&type=${transactionType}`);

      if (response.status === 200) {
        return response.data;
      } else {
        console.log('Failed to fetch yearly chart data:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error fetching yearly chart data:', error);
      return [];
    }
  }
}

export default new TransactionService();
