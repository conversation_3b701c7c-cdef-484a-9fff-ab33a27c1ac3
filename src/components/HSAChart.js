import React, { useState, useEffect, useRef, useMemo, forwardRef, useImperativeHandle, useLayoutEffect } from "react";
import { Text, TouchableOpacity, View, StyleSheet, Dimensions, Animated, ScrollView, Modal } from "react-native";
import { Bar<PERSON>hart } from "react-native-gifted-charts";
import { SymbolView } from "expo-symbols";
import SegmentedControl from "@react-native-segmented-control/segmented-control";
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import transactionService from "../services/transactionService";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Enum equivalent in JavaScript
const Period = {
  week: "week",
  month: "month",
  year: "year",
};

// Cache configuration
const CACHE_CONFIG = {
  maxWeekEntries: 20,    // Cache 20 weeks (about 5 months)
  maxYearEntries: 5,     // Cache 5 years
  preloadRange: 2,       // Preload 2 periods ahead/behind
};

// Format bar value for the label shown above each bar. Values ≥ 1000 are abbreviated, e.g., 1.25k.
const formatBarLabel = (value) => {
  if (value >= 1000) {
    // Keep up to two decimals, strip trailing zeros
    const formatted = (value / 1000)
      .toFixed(2)
      .replace(/\.0+$|0+$/,'');
    return `$${formatted}k`;
  }
  return `$${value}`;
};

const HSAChart = forwardRef(({ data, loading, defaultPeriod = Period.week, initialWeek = null }, ref) => {
  // Initialize all state values
  const [chartPeriod, setChartPeriod] = useState(defaultPeriod || Period.week);
  const [barData, setBarData] = useState([]);
  const [currentDate, setCurrentDate] = useState(initialWeek || new Date());
  const [currentEndDate, setCurrentEndDate] = useState(new Date());
  const [chartKey, setChartKey] = useState(0);
  const [transactionType, setTransactionType] = useState("Expense");
  const [totalAmount, setTotalAmount] = useState(0);
  const [yearDataLoaded, setYearDataLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [yearPickerVisible, setYearPickerVisible] = useState(false);
  const [allYears, setAllYears] = useState([]);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const totalAmountAnim = useRef(new Animated.Value(0)).current;
  const [isAnimating, setIsAnimating] = useState(false);
  const [animatedBarData, setAnimatedBarData] = useState([]);
  const [displayedTotal, setDisplayedTotal] = useState(0);
  
  // Store current state values in refs to avoid accessing state during render
  const transactionTypeRef = useRef(transactionType);
  
  // Update refs when state changes
  useEffect(() => {
    transactionTypeRef.current = transactionType;
  }, [transactionType]);

  // Cache for storing fetched data
  const dataCache = useRef({
    week: new Map(),    // Key: "startDate-endDate-type", Value: data
    month: new Map(),   // Key: "year-month-type", Value: data  
    year: new Map(),    // Key: "year-type", Value: data
  });

  // Track ongoing requests to prevent duplicates
  const ongoingRequests = useRef(new Set());

  // Use useLayoutEffect for all initial state setup that might cause updates
  useLayoutEffect(() => {
    // Set initial values for animations
    totalAmountAnim.setValue(totalAmount);
    setDisplayedTotal(totalAmount);
  }, [totalAmount]);

  // Use useLayoutEffect for initializing animatedBarData
  useLayoutEffect(() => {
    if (barData.length > 0) {
      setAnimatedBarData(barData);
    }
  }, [barData]);

  // Generate cache key for different periods
  const generateCacheKey = (period, date, type, startDate = null, endDate = null) => {
    switch (period) {
      case Period.week:
        return `${startDate}-${endDate}-${type}`;
      case Period.month:
        return `${date.getFullYear()}-${date.getMonth()}-${type}`;
      case Period.year:
        return `${date.getFullYear()}-${type}`;
      default:
        return `${date.getTime()}-${type}`;
    }
  };

  // Clean up old cache entries to prevent memory issues
  const cleanupCache = (period) => {
    const cache = dataCache.current[period];
    const maxEntries = period === Period.week ? CACHE_CONFIG.maxWeekEntries : CACHE_CONFIG.maxYearEntries;
    
    if (cache.size > maxEntries) {
      const entries = Array.from(cache.entries());
      // Remove oldest entries (first half)
      const toRemove = entries.slice(0, Math.floor(entries.length / 2));
      toRemove.forEach(([key]) => cache.delete(key));
    }
  };

  // Animation helper functions
  const animateChartTransition = (newData, newTotal, isFromCache = false) => {
    // Set animating state first
    setIsAnimating(true);
    
    // Reset animation values to ensure full opacity during transition
    fadeAnim.setValue(1);
    scaleAnim.setValue(1);
    
    // Immediately set empty data to prevent any old bars from showing during transition
    if (chartPeriod === Period.week) {
      const emptyData = Array(7).fill().map((_, i) => ({
        value: 0,
        label: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
        frontColor: transactionType === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: transactionType === 'Expense' ? '#dc2626' : '#16a34a',
        dayIndex: i,
        topLabelComponent: () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>$0</Text>
          </View>
        )
      }));
      setAnimatedBarData(emptyData);
      setBarData(emptyData);
    }
    
    // Animation duration based on whether data is from cache or fresh
    const duration = isFromCache ? 400 : 600;
    
    // Use setTimeout to ensure this doesn't happen during render
    setTimeout(() => {
      // Skip the fade out animation and directly update data
      setBarData(newData);
      
      // Small delay before showing the new data
      setTimeout(() => {
        setAnimatedBarData(newData);
        
        // Animate total amount
        Animated.timing(totalAmountAnim, {
          toValue: newTotal,
          duration: duration * 0.7,
          useNativeDriver: false,
        }).start();
        
        // End the animation state
        setIsAnimating(false);
      }, 50);
    }, 0);
  };

  // Animate bar values individually for smoother effect
  const animateBarValues = (newData, isFromCache = false) => {
    if (!newData || newData.length === 0) {
      setAnimatedBarData([]);
      return;
    }

    // For simpler implementation, just use the chart's built-in animation
    // The BarChart component already has isAnimated={true} and animationDuration={300}
    setAnimatedBarData(newData);
  };

  // Fetch data with caching
  const fetchDataWithCache = async (period, date, type, startDate = null, endDate = null, skipPreload = false) => {
    const cacheKey = generateCacheKey(period, date, type, startDate, endDate);
    const cache = dataCache.current[period];

    // Return cached data if available
    if (cache.has(cacheKey)) {
      // Use cached data instantly without loading state or chart re-render
      const cachedData = cache.get(cacheKey);
      let processedData;
      let totalValue = 0;
      
      if (period === Period.week) {
        const { startDate } = getWeekRange(date);
        setCurrentEndDate(new Date(startDate));
        processedData = processWeeklyData(cachedData, type);
        
        if (cachedData && cachedData.length > 0) {
          totalValue = cachedData.reduce((sum, item) => sum + (item.total || 0), 0);
        }
        setYearDataLoaded(false);
      } else if (period === Period.month) {
        processedData = processMonthlyData(cachedData, type);
        totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
        setYearDataLoaded(false);
      } else if (period === Period.year) {
        processedData = processYearlyData(cachedData, type);
        totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
        setYearDataLoaded(true);
      }
      
      // Animate the transition with cached data (faster animation)
      animateChartTransition(processedData, totalValue, true);
      
      // Only preload adjacent data if not already preloading (prevent recursion)
      if (!skipPreload) {
        preloadAdjacentData(period, date, type);
      }
      return;
    }

    // Prevent duplicate requests
    if (ongoingRequests.current.has(cacheKey)) {
      return null;
    }

    ongoingRequests.current.add(cacheKey);

    try {
      let fetchedData;
      
      switch (period) {
        case Period.week:
          fetchedData = await transactionService.getWeeklyChartData(startDate, endDate, type);
          break;
        case Period.month:
          fetchedData = await transactionService.getMonthlyChartData(type);
          break;
        case Period.year:
          fetchedData = await transactionService.getYearlyChartData(date.getFullYear(), type);
          break;
        default:
          fetchedData = [];
      }

      // Cache the data
      cache.set(cacheKey, fetchedData);
      cleanupCache(period);

      return fetchedData;
    } catch (error) {
      console.error(`Error fetching ${period} data:`, error);
      return [];
    } finally {
      ongoingRequests.current.delete(cacheKey);
    }
  };

  // Preload adjacent periods for smooth navigation
  const preloadAdjacentData = async (period, date, type) => {
    const preloadPromises = [];

    for (let i = 1; i <= CACHE_CONFIG.preloadRange; i++) {
      // Preload previous periods
      const prevDate = new Date(date);
      const nextDate = new Date(date);

      if (period === Period.week) {
        prevDate.setDate(date.getDate() - (7 * i));
        nextDate.setDate(date.getDate() + (7 * i));
        
        const prevWeekRange = getWeekRange(prevDate);
        const nextWeekRange = getWeekRange(nextDate);
        
        preloadPromises.push(
          fetchDataWithCache(period, prevDate, type, prevWeekRange.startDate, prevWeekRange.endDate, true),
          fetchDataWithCache(period, nextDate, type, nextWeekRange.startDate, nextWeekRange.endDate, true)
        );
      } else if (period === Period.year) {
        prevDate.setFullYear(date.getFullYear() - i);
        nextDate.setFullYear(date.getFullYear() + i);
        
        preloadPromises.push(
          fetchDataWithCache(period, prevDate, type, null, null, true),
          fetchDataWithCache(period, nextDate, type, null, null, true)
        );
      }
    }

    // Execute preloading in background (don't await)
    Promise.all(preloadPromises).catch(error => {
      console.log('Preloading failed (non-critical):', error);
    });
  };

  // Main data loading function with caching
  const loadData = async (period, date, type, forceLoad = false) => {
    // Check if data is already cached and we're not forcing a reload
    if (!forceLoad) {
      const cacheKey = generateCacheKey(period, date, type, 
        period === Period.week ? getWeekRange(date).startDate : null,
        period === Period.week ? getWeekRange(date).endDate : null
      );
      const cache = dataCache.current[period];
      
      if (cache.has(cacheKey)) {
        // Use cached data instantly without loading state or chart re-render
        const cachedData = cache.get(cacheKey);
        let processedData;
        let totalValue = 0;
        
        if (period === Period.week) {
          const { startDate } = getWeekRange(date);
          setCurrentEndDate(new Date(startDate));
          processedData = processWeeklyData(cachedData, type);
          
          if (cachedData && cachedData.length > 0) {
            totalValue = cachedData.reduce((sum, item) => sum + (item.total || 0), 0);
          }
          setYearDataLoaded(false);
        } else if (period === Period.month) {
          processedData = processMonthlyData(cachedData, type);
          totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
          setYearDataLoaded(false);
        } else if (period === Period.year) {
          processedData = processYearlyData(cachedData, type);
          totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
          setYearDataLoaded(true);
        }
        
        // Animate the transition with cached data (faster animation)
        animateChartTransition(processedData, totalValue, true);
        
        // Still preload adjacent data in background
        preloadAdjacentData(period, date, type);
        return;
      }
    }

    // Only show loading state if data is not cached
    setIsLoading(true);

    try {
      let fetchedData;
      let processedData;

      if (period === Period.week) {
        const { startDate, endDate } = getWeekRange(date);
        setCurrentEndDate(new Date(startDate));
        
        fetchedData = await fetchDataWithCache(period, date, type, startDate, endDate);
        processedData = processWeeklyData(fetchedData, type);
        
        // Calculate total for the week
        let totalValue = 0;
        if (fetchedData && fetchedData.length > 0) {
          totalValue = fetchedData.reduce((sum, item) => sum + (item.total || 0), 0);
        }
        setYearDataLoaded(false);
        
        // Animate the transition with new data
        animateChartTransition(processedData, totalValue, false);
        
        // Preload adjacent weeks
        preloadAdjacentData(period, date, type);
        
      } else if (period === Period.month) {
        fetchedData = await fetchDataWithCache(period, date, type);
        processedData = processMonthlyData(fetchedData, type);
        const totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
        setYearDataLoaded(false);
        
        // Animate the transition with new data
        animateChartTransition(processedData, totalValue, false);
        
      } else if (period === Period.year) {
        setYearDataLoaded(false);
        fetchedData = await fetchDataWithCache(period, date, type);
        processedData = processYearlyData(fetchedData, type);
        const totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
        setYearDataLoaded(true);
        
        // Animate the transition with new data
        animateChartTransition(processedData, totalValue, false);
        
        // Preload adjacent years
        preloadAdjacentData(period, date, type);
      }

      // Only increment chartKey for new data loads (not cached data)
      setChartKey(prev => prev + 1);
      
    } catch (error) {
      console.error('Error loading data:', error);
      setBarData([]);
      setTotalAmount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data when period or type changes (but not when just navigating dates)
  useEffect(() => {
    // Schedule the data loading in the next tick to avoid render-time state updates
    setTimeout(() => {
      loadData(chartPeriod, currentDate, transactionType, true); // Force load on period/type change
    }, 0);
  }, [chartPeriod, transactionType]);
  
  // Always start with current week data on mount
  useEffect(() => {
    if (chartPeriod === Period.week) {
      const today = new Date();
      setCurrentDate(today);
      loadData(Period.week, today, transactionType, true);
    }
  }, []);

  // Handle date navigation separately without triggering loading state
  useEffect(() => {
    // Only load data for date changes, not period/type changes
    if (chartPeriod && transactionType) {
      // When date changes, update currentEndDate for proper week display
      if (chartPeriod === Period.week) {
        const weekRange = getWeekRange(currentDate);
        setCurrentEndDate(new Date(weekRange.startDate));
      }
      
      // Schedule the data loading in the next tick to avoid render-time state updates
      setTimeout(() => {
        loadData(chartPeriod, currentDate, transactionType, false); // Don't force load, use cache if available
      }, 0);
    }
  }, [currentDate]);

  // Force chart re-render when year data is loaded to apply initialSpacing
  useEffect(() => {
    if (yearDataLoaded && chartPeriod === Period.year) {
      // Small delay to ensure data is fully rendered, then trigger re-render
      const timer = setTimeout(() => {
        setChartKey(prev => prev + 1);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [yearDataLoaded, chartPeriod]);

  // Load available years on component mount
  useEffect(() => {
    loadAvailableYears();
  }, []);

  const loadAvailableYears = async () => {
    try {
      const years = await transactionService.getAvailableYears();
      if (years && years.length > 0) {
        // Sort years in descending order
        years.sort((a, b) => b - a);
        setAllYears(years);
        
        // If current selected year is not in the list, select the most recent year
        if (!years.includes(selectedYear)) {
          setSelectedYear(years[0]);
          const newDate = new Date(currentDate);
          newDate.setFullYear(years[0]);
          setCurrentDate(newDate);
        }
      } else {
        // If no years available, use current year
        const currentYear = new Date().getFullYear();
        setAllYears([currentYear]);
        setSelectedYear(currentYear);
      }
    } catch (error) {
      console.error('Error loading available years:', error);
      // Fallback to current year if API fails
      const currentYear = new Date().getFullYear();
      setAllYears([currentYear]);
      setSelectedYear(currentYear);
    }
  };

  // Update current date when selected year changes
  useEffect(() => {
    if (chartPeriod === Period.year) {
      const newDate = new Date(selectedYear, 0, 1); // January 1st of selected year
      setCurrentDate(newDate);
    }
  }, [selectedYear, chartPeriod]);

  const calculateTotal = (data) => {
    const sum = data.reduce((total, item) => total + item.value, 0);
    setTotalAmount(sum);
  };

  const getWeekRange = (date) => {
    // Create a new date object to avoid modifying the original
    const currentDate = new Date(date);
    
    // Get the current day of the week (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeek = currentDate.getDay();
    
    // Calculate the date of the Sunday that starts this week
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - dayOfWeek);
    startOfWeek.setHours(0, 0, 0, 0); // Set to beginning of the day
    
    // Calculate the date of the Saturday that ends this week
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999); // Set to end of the day
    
    return {
      startDate: startOfWeek.getTime(),
      endDate: endOfWeek.getTime(),
    };
  };

  const getMonthRange = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const startOfMonth = new Date(year, month, 1);
    const endOfMonth = new Date(year, month + 1, 0);
    
    return {
      startDate: startOfMonth.getTime(),
      endDate: endOfMonth.getTime(),
    };
  };

  const handlePrevious = () => {
    // Set animating state immediately
    setIsAnimating(true);
    
    const newDate = new Date(currentDate);
    if (chartPeriod === Period.week) {
      // Clear bar data immediately to prevent old bars from showing
      const emptyData = Array(7).fill().map((_, i) => ({
        value: 0,
        label: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
        frontColor: transactionType === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: transactionType === 'Expense' ? '#dc2626' : '#16a34a',
        dayIndex: i,
      }));
      setAnimatedBarData(emptyData);
      setBarData(emptyData);
      
      // Get the current week's Sunday
      const currentWeekRange = getWeekRange(currentDate);
      const currentSunday = new Date(currentWeekRange.startDate);
      
      // Set to previous Sunday (7 days before current Sunday)
      const prevSunday = new Date(currentSunday);
      prevSunday.setDate(currentSunday.getDate() - 7);
      
      setCurrentDate(prevSunday);
    } else if (chartPeriod === Period.month) {
      newDate.setMonth(currentDate.getMonth() - 1);
      setCurrentDate(newDate);
    } else if (chartPeriod === Period.year) {
      newDate.setFullYear(currentDate.getFullYear() - 1);
      setCurrentDate(newDate);
    }
  };

  const handleNext = () => {
    // Set animating state immediately
    setIsAnimating(true);
    
    const newDate = new Date(currentDate);
    if (chartPeriod === Period.week) {
      // Clear bar data immediately to prevent old bars from showing
      const emptyData = Array(7).fill().map((_, i) => ({
        value: 0,
        label: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
        frontColor: transactionType === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: transactionType === 'Expense' ? '#dc2626' : '#16a34a',
        dayIndex: i,
      }));
      setAnimatedBarData(emptyData);
      setBarData(emptyData);
      
      // Get the current week's Sunday
      const currentWeekRange = getWeekRange(currentDate);
      const currentSunday = new Date(currentWeekRange.startDate);
      
      // Set to next Sunday (7 days after current Sunday)
      const nextSunday = new Date(currentSunday);
      nextSunday.setDate(currentSunday.getDate() + 7);
      
      // Don't allow navigating to future weeks beyond current week
      const today = new Date();
      const currentWeekSunday = new Date(getWeekRange(today).startDate);
      
      if (nextSunday <= currentWeekSunday) {
        setCurrentDate(nextSunday);
      }
    } else if (chartPeriod === Period.month) {
      newDate.setMonth(currentDate.getMonth() + 1);
      
      // Don't allow navigating to future months beyond current month
      const today = new Date();
      if (newDate.getFullYear() < today.getFullYear() || 
          (newDate.getFullYear() === today.getFullYear() && 
           newDate.getMonth() <= today.getMonth())) {
        setCurrentDate(newDate);
      }
    } else if (chartPeriod === Period.year) {
      newDate.setFullYear(currentDate.getFullYear() + 1);
      
      // Don't allow navigating to future years beyond current year
      const today = new Date();
      if (newDate.getFullYear() <= today.getFullYear()) {
        setCurrentDate(newDate);
      }
    }
  };

  // Handle swipe gestures
  const handleSwipeGesture = (event) => {
    const { translationX, translationY, velocityX, velocityY, state } = event.nativeEvent;
    
    if (state === State.END) {
      // Check if this is primarily a horizontal gesture
      const isHorizontalGesture = Math.abs(translationX) > Math.abs(translationY);
      const isSignificantHorizontalMovement = Math.abs(translationX) > 50;
      const isSignificantHorizontalVelocity = Math.abs(velocityX) > 500;
      
      // Only trigger navigation if it's clearly a horizontal swipe
      if (isHorizontalGesture && (isSignificantHorizontalMovement || isSignificantHorizontalVelocity)) {
        if (translationX > 0) {
          // Swipe right - go to previous period
          handlePrevious();
        } else {
          // Swipe left - go to next period
          handleNext();
        }
      }
    }
  };

  const processWeeklyData = (data, type) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    // Get the week range to display actual dates
    const weekRange = getWeekRange(currentDate);
    const weekStart = new Date(weekRange.startDate);
    
    // Create default data structure with all days initialized to 0
    const defaultData = days.map((day, index) => {
      const roundedValue = 0;
      
      // Calculate the actual date for this day of the week
      const dayDate = new Date(weekStart);
      dayDate.setDate(weekStart.getDate() + index);
      const dayNumber = dayDate.getDate();
      
      // Create a label with day name and date number on separate lines
      const dayLabel = day;
      
      return {
        value: roundedValue,
        label: dayLabel,
        frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
        dayIndex: index,
        dayDate: dayDate,
        topLabelComponent: () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        )
      };
    });
    
    // If no data is returned, use the default structure
    if (!data || data.length === 0) {
      return defaultData;
    }
    
    // Fill in actual values where they exist
    data.forEach(item => {
      if (item.dayOfWeek >= 0 && item.dayOfWeek < 7) {
        const roundedValue = Math.round(item.total);
        defaultData[item.dayOfWeek].value = roundedValue;
        defaultData[item.dayOfWeek].topLabelComponent = () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        );
      }
    });
    
    return defaultData;
  };

  const processMonthlyData = (data, type) => {
    if (!data || data.length === 0) {
      // Generate default data for 12 months if no data is available
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return monthNames.map(month => ({
        value: 0,
        label: month,
        frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
      }));
    }

    // Show all 12 months of data (full year)
    return data.map(item => ({
      value: type === 'Expense' ? (item.expenses || 0) : (item.contributions || 0),
      label: item.month,
      frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
      gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
    }));
  };

  const processYearlyData = (data, type) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Create default data structure with all months initialized to 0
    const defaultData = monthNames.map(month => ({
      value: 0,
      label: month,
      frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
      gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
    }));
    
    // If no data is returned, use the default structure
    if (!data || data.length === 0) {
      return defaultData;
    }
    
    // Fill in actual values where they exist
    data.forEach(item => {
      const monthIndex = parseInt(item.month) - 1; // Convert month (1-12) to array index (0-11)
      if (monthIndex >= 0 && monthIndex < 12) {
        defaultData[monthIndex].value = type === 'Expense' ? (item.expenses || 0) : (item.contributions || 0);
      }
    });
    
    return defaultData;
  };

  const getPeriodTitle = () => {
    if (chartPeriod === Period.week) {
      // Get the current week's Sunday and Saturday
      const weekRange = getWeekRange(currentDate);
      const startDate = new Date(weekRange.startDate);
      const endDate = new Date(weekRange.endDate);
      
      // Format dates properly
      const startMonth = startDate.toLocaleDateString("en-US", { month: "short" });
      const endMonth = endDate.toLocaleDateString("en-US", { month: "short" });
      const startDay = startDate.getDate();
      const endDay = endDate.getDate();
      const year = startDate.getFullYear();
      
      // Handle case where week spans two different months
      if (startMonth === endMonth) {
        return `${startMonth} ${startDay}-${endDay}, ${year}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
      }
    } else if (chartPeriod === Period.month) {
      return currentDate.toLocaleDateString("en-US", { month: "long", year: "numeric" });
    } else if (chartPeriod === Period.year) {
      return selectedYear.toString();
    }
    return "";
  };

  // Generate years from 1950 to 2099
  const generateAllYears = () => {
    const years = [];
    for (let year = 1950; year <= 2099; year++) {
      years.push(year);
    }
    return years;
  };

  // Handle year selection
  const handleYearChange = (year) => {
    setSelectedYear(year);
    setYearPickerVisible(false);
    const newDate = new Date(currentDate);
    newDate.setFullYear(year);
    setCurrentDate(newDate);
  };

  // Initialize years on mount
  useEffect(() => {
    setAllYears(generateAllYears());
  }, []);

  // Year picker modal
  const renderYearPickerModal = () => (
    <Modal
      visible={yearPickerVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setYearPickerVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.yearPickerContainer}>
          <View style={styles.yearPickerHeader}>
            <Text style={styles.yearPickerTitle}>Select Year</Text>
            <TouchableOpacity 
              style={styles.closeButtonContainer}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.yearPickerContent}>
            {/* Decades quick selector */}
            <View style={styles.decadesSelector}>
              {[1950, 1970, 1990, 2010, 2030, 2050, 2070, 2090].map(decade => (
                <TouchableOpacity
                  key={decade}
                  style={[
                    styles.decadeButton,
                    selectedYear >= decade && selectedYear < decade + 20 && styles.decadeButtonActive
                  ]}
                  onPress={() => setSelectedYear(decade)}
                >
                  <Text style={styles.decadeButtonText}>{decade}s</Text>
                </TouchableOpacity>
              ))}
            </View>
            
            {/* Year grid */}
            <View style={styles.yearGrid}>
              {allYears
                .filter(year => year >= Math.floor(selectedYear / 10) * 10 && year < Math.floor(selectedYear / 10) * 10 + 10)
                .map(year => (
                  <TouchableOpacity
                    key={year}
                    style={[
                      styles.yearGridItem,
                      selectedYear === year && styles.yearGridItemSelected
                    ]}
                    onPress={() => handleYearChange(year)}
                  >
                    <Text
                      style={[
                        styles.yearGridItemText,
                        selectedYear === year && styles.yearGridItemTextSelected,
                        year === new Date().getFullYear() && styles.currentYearText
                      ]}
                    >
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))
              }
            </View>
          </View>
          
          <View style={styles.yearPickerActions}>
            <TouchableOpacity
              style={styles.yearPickerButton}
              onPress={() => handleYearChange(new Date().getFullYear())}
            >
              <Text style={styles.yearPickerButtonText}>Current Year</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.yearPickerButton, styles.yearPickerPrimaryButton]}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={[styles.yearPickerButtonText, styles.yearPickerPrimaryButtonText]}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    setToCurrentWeek: () => {
      // Use a function to update state to avoid issues
      setChartPeriod(Period.week);
      const today = new Date();
      setCurrentDate(today);
      
      // Schedule the data loading in the next tick to avoid render-time state updates
      setTimeout(() => {
        loadData(Period.week, today, transactionTypeRef.current, true);
      }, 0);
    }
  }));

  // Initialize with the specified period and date
  useEffect(() => {
    if (defaultPeriod) {
      setChartPeriod(defaultPeriod);
    }
    
    // Always use today's date to ensure we're showing the current week
    const today = new Date();
    setCurrentDate(today);
    
    // Force load data for the current week on first render
    if (defaultPeriod === Period.week) {
      // Schedule the data loading in the next tick to avoid render-time state updates
      setTimeout(() => {
        loadData(Period.week, today, transactionTypeRef.current, true);
      }, 0);
    }
  }, [defaultPeriod]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>
        
        <SegmentedControl
          values={["Week", "Year"]}
          style={styles.segmentedControl}
          selectedIndex={chartPeriod === Period.week ? 0 : 1}
          onChange={(event) => {
            const index = event.nativeEvent.selectedSegmentIndex;
            setChartPeriod(index === 0 ? Period.week : Period.year);
          }}
        />
        
        <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
        <Text style={styles.periodSubtitle}>
          Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
        </Text>

        <Text style={styles.totalAmount}>$0.00</Text>
        
        <View style={styles.chartContainer}>
          <BarChart
            data={Array(7).fill().map((_, i) => ({
              value: 0,
              label: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
              frontColor: '#e5e7eb',
              gradientColor: '#d1d5db',
              dayIndex: i,
            }))}
            barWidth={chartPeriod === Period.week ? 24 : 18}
            height={220}
            width={SCREEN_WIDTH - 40}
            minHeight={3}
            barBorderRadius={4}
            showGradient
            spacing={chartPeriod === Period.week ? 18 : chartPeriod === Period.month ? 20 : 9}
            noOfSections={4}
            yAxisThickness={0}
            xAxisThickness={0}
            xAxisLabelsVerticalShift={5}
            xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
            yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
            horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
          />
          <View style={styles.loadingOverlay}>
            <Text style={styles.loadingText}>Loading chart data...</Text>
          </View>
        </View>
        
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            disabled={true}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.left.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"#d1d5db"}
            />
            <Text style={[styles.controlButtonText, {color: '#d1d5db'}]}>Prev {chartPeriod}</Text>
          </TouchableOpacity>
          
          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />
          
          <TouchableOpacity
            disabled={true}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.right.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"#d1d5db"}
            />
            <Text style={[styles.controlButtonText, {color: '#d1d5db'}]}>Next {chartPeriod}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (chartPeriod === Period.year) {
    return (
      <ScrollView style={styles.yearViewContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>
          <TouchableOpacity 
            style={styles.yearSelectorButton}
            onPress={() => setYearPickerVisible(true)}
          >
            <Text style={styles.yearSelectorText}>{selectedYear}</Text>
            <Text style={styles.yearSelectorIcon}>▼</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>
            Total HSA {transactionType === "Expense" ? "Spending" : "Contributions"} - {selectedYear}
          </Text>
          <Text style={styles.summaryAmount}>${displayedTotal.toFixed(2)}</Text>
        </View>
        
        <View style={styles.chartCard}>
          <Text style={styles.chartTitle}>Monthly {transactionType === "Expense" ? "Expenses" : "Contributions"}</Text>
          
          <View style={styles.chartOuterContainer}>
            <Animated.View 
              style={[
                styles.chartContainer,
                { 
                  opacity: fadeAnim,
                  transform: [{ scale: scaleAnim }]
                }
              ]}
            >
              <BarChart
                key={chartKey}
                data={animatedBarData.length > 0 ? animatedBarData : barData}
                barWidth={18}
                height={220}
                width={SCREEN_WIDTH - 48}
                minHeight={3}
                barBorderRadius={4}
                showGradient
                spacing={10}
                noOfSections={4}
                yAxisThickness={0}
                xAxisThickness={0}
                xAxisLabelsVerticalShift={5}
                xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
                yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
                horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
                xAxisLabelComponent={(item) => (
                  <Text style={{ color: "gray", fontSize: 12 }}>{item.label}</Text>
                )}
                isAnimated
                animationDuration={600}
                topLabelContainerStyle={{ height: 30 }}
                scrollToEnd={false}
                disableScroll={false}
                leftShiftForLastIndexToFit={true}
                formatYLabel={(value) => value.toLocaleString()}
                maxValue={(() => {
                  const NUMBER_OF_SECTIONS = 4;
                  const maxVal = Math.max(...barData.map(item => item.value), 0);
                  
                  let roundedMax;
                  
                  if (maxVal <= 100) {
                    roundedMax = Math.ceil(maxVal / 25) * 25;
                  } else if (maxVal <= 1000) {
                    roundedMax = Math.ceil(maxVal / 100) * 100;
                  } else if (maxVal <= 10000) {
                    roundedMax = Math.ceil(maxVal / 500) * 500;
                  } else {
                    roundedMax = Math.ceil(maxVal / 1000) * 1000;
                  }
                  
                  const remainder = roundedMax % NUMBER_OF_SECTIONS;
                  if (remainder !== 0) {
                    roundedMax = roundedMax + (NUMBER_OF_SECTIONS - remainder);
                  }
                  
                  const sectionSize = roundedMax / NUMBER_OF_SECTIONS;
                  return roundedMax + sectionSize;
                })()}
                initialSpacing={yearDataLoaded ? (() => {
                  const currentMonth = new Date().getMonth();
                  const barWidth = 18;
                  const spacing = 9;
                  const totalBarWidth = barWidth + spacing;
                  const screenWidth = SCREEN_WIDTH - 64;
                  const visibleBars = Math.floor(screenWidth / totalBarWidth);
                  const centerOffset = Math.floor(visibleBars / 2);
                  
                  const targetPosition = Math.max(0, currentMonth - centerOffset);
                  return targetPosition * totalBarWidth;
                })() : 0}
              />
            </Animated.View>
            
            {isAnimating && (
              <View style={styles.fullLoadingOverlay}>
                <Text style={styles.loadingText}>Loading...</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.controlsContainer}>
          <SegmentedControl
            values={["Week", "Year"]}
            style={styles.segmentedControl}
            selectedIndex={chartPeriod === Period.week ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setChartPeriod(index === 0 ? Period.week : Period.year);
            }}
          />
          
          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />
        </View>

        {renderYearPickerModal()}
      </ScrollView>
    );
  }

  return (
    <PanGestureHandler
      onGestureEvent={handleSwipeGesture}
      onHandlerStateChange={handleSwipeGesture}
      activeOffsetX={[-20, 20]}
      failOffsetY={[-20, 20]}
      shouldCancelWhenOutside={true}
    >
      <View style={styles.container}>
        <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>
        
        <SegmentedControl
          values={["Week", "Year"]}
          style={styles.segmentedControl}
          selectedIndex={chartPeriod === Period.week ? 0 : 1}
          onChange={(event) => {
            const index = event.nativeEvent.selectedSegmentIndex;
            setChartPeriod(index === 0 ? Period.week : Period.year);
          }}
        />
        
        <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
        <Text style={styles.periodSubtitle}>
          Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
        </Text>

        <Animated.Text style={[styles.totalAmount, { opacity: fadeAnim }]}>
          ${displayedTotal.toFixed(2)}
        </Animated.Text>
        
        <View style={styles.swipeHintContainer}>
          <Text style={styles.swipeHintText}>← Swipe to navigate →</Text>
        </View>
        
        <View style={styles.chartOuterContainer}>
          <Animated.View 
            style={[
              styles.chartContainer,
              { 
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <BarChart
              key={chartKey}
              data={animatedBarData.length > 0 ? animatedBarData : barData}
              barWidth={chartPeriod === Period.week ? 24 : 18}
              height={220}
              width={SCREEN_WIDTH - 40}
              minHeight={3}
              barBorderRadius={4}
              showGradient
              spacing={chartPeriod === Period.week ? 18 : chartPeriod === Period.month ? 20 : 9}
              noOfSections={4}
              yAxisThickness={0}
              xAxisThickness={0}
              xAxisLabelsVerticalShift={5}
              xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
              yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
              horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
              xAxisLabelComponent={(item) => {
                if (chartPeriod === Period.week && item.dayDate) {
                  return (
                    <View style={{ alignItems: 'center', width: 40, paddingHorizontal: 2 }}>
                      <Text style={{ color: "#666", fontSize: 11, fontWeight: '500' }}>{item.label}</Text>
                      <Text style={{ color: "#666", fontSize: 13, fontWeight: '600', marginTop: 3 }}>{item.dayDate.getDate()}</Text>
                    </View>
                  );
                }
                return <Text style={{ color: "gray", fontSize: 12 }}>{item.label}</Text>;
              }}
              isAnimated
              animationDuration={600}
              topLabelContainerStyle={{ height: 30 }}
              scrollToEnd={false}
              disableScroll={chartPeriod === Period.week}
              initialSpacing={chartPeriod === Period.week ? 16 : undefined}
              leftShiftForLastIndexToFit={chartPeriod === Period.week ? true : undefined}
              formatYLabel={(value) => {
                return value.toLocaleString();
              }}
              maxValue={(() => {
                const NUMBER_OF_SECTIONS = 4;
                const maxVal = Math.max(...barData.map(item => item.value), 0);
                
                let roundedMax;
                
                if (maxVal <= 100) {
                  roundedMax = Math.ceil(maxVal / 25) * 25;
                } else if (maxVal <= 1000) {
                  roundedMax = Math.ceil(maxVal / 100) * 100;
                } else if (maxVal <= 10000) {
                  roundedMax = Math.ceil(maxVal / 500) * 500;
                } else {
                  roundedMax = Math.ceil(maxVal / 1000) * 1000;
                }
                
                const remainder = roundedMax % NUMBER_OF_SECTIONS;
                if (remainder !== 0) {
                  roundedMax = roundedMax + (NUMBER_OF_SECTIONS - remainder);
                }
                
                const sectionSize = roundedMax / NUMBER_OF_SECTIONS;
                return roundedMax + sectionSize;
              })()}
            />
          </Animated.View>
          
          {isAnimating && (
            <View style={styles.fullLoadingOverlay}>
              <Text style={styles.loadingText}>Loading...</Text>
            </View>
          )}
        </View>
        
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            onPress={handlePrevious}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.left.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"gray"}
            />
            <Text style={styles.controlButtonText}>Prev {chartPeriod}</Text>
          </TouchableOpacity>
          
          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />
          
          <TouchableOpacity
            onPress={handleNext}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.right.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"gray"}
            />
            <Text style={styles.controlButtonText}>Next {chartPeriod}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </PanGestureHandler>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
  },
  segmentedControl: {
    marginBottom: 16,
  },
  yearSegmentedControl: {
    marginBottom: 16,
    width: 200
  },
  periodTitle: {
    fontWeight: "700", 
    fontSize: 18, 
    marginBottom: 8
  },
  periodSubtitle: {
    color: "gray"
  },
  totalAmount: {
    fontWeight: "700", 
    fontSize: 32, 
    marginBottom: 16
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "baseline",
    marginTop: 16,
  },
  controlButton: {
    alignItems: "center"
  },
  controlButtonText: {
    fontSize: 11,
    color: "gray"
  },
  typeSegmentedControl: {
    width: 200
  },
  loadingContainer: {
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  barValueLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  swipeHintContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  swipeHintText: {
    fontSize: 14,
    color: 'gray',
  },
  yearViewContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  yearSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  yearSelectorText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginRight: 8,
  },
  yearSelectorIcon: {
    fontSize: 12,
    color: '#64748B',
  },
  summaryCard: {
    backgroundColor: '#f7f7f7',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333',
  },
  chartCard: {
    backgroundColor: '#f7f7f7',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    position: 'relative',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  chartContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingTop: 20,
    paddingBottom: 10,
    overflow: 'hidden',
  },
  chartOuterContainer: {
    position: 'relative',
  },
  fullLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    zIndex: 10,
    borderRadius: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearPickerContainer: {
    width: SCREEN_WIDTH * 0.85,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  yearPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  yearPickerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A2138',
  },
  closeButtonContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    fontSize: 18,
    color: '#64748B',
    fontWeight: '600',
  },
  yearPickerContent: {
    padding: 16,
  },
  decadesSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  decadeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginBottom: 8,
    backgroundColor: '#F1F5F9',
  },
  decadeButtonActive: {
    backgroundColor: '#EEF2FF',
  },
  decadeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  yearGridItem: {
    width: '20%',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  yearGridItemSelected: {
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
  },
  yearGridItemText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#64748B',
  },
  yearGridItemTextSelected: {
    color: '#4C6FFF',
    fontWeight: '700',
  },
  currentYearText: {
    textDecorationLine: 'underline',
  },
  yearPickerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  yearPickerButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  yearPickerPrimaryButton: {
    backgroundColor: '#4C6FFF',
  },
  yearPickerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748B',
  },
  yearPickerPrimaryButtonText: {
    color: '#FFFFFF',
  },
});

export default HSAChart;
