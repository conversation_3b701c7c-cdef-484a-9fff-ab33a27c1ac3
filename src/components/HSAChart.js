import React, { useState, useEffect, useRef, useMemo, forwardRef, useImperativeHandle, useLayoutEffect } from "react";
import { Text, TouchableOpacity, View, StyleSheet, Dimensions, Animated, ScrollView, Modal } from "react-native";
import { Bar<PERSON>hart } from "react-native-gifted-charts";
import { SymbolView } from "expo-symbols";
import SegmentedControl from "@react-native-segmented-control/segmented-control";
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import transactionService from "../services/transactionService";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Enum equivalent in JavaScript
const Period = {
  week: "week",
  month: "month",
  year: "year",
};

// Cache configuration
const CACHE_CONFIG = {
  maxWeekEntries: 20,    // Cache 20 weeks (about 5 months)
  maxYearEntries: 5,     // Cache 5 years
  preloadRange: 2,       // Preload 2 periods ahead/behind
};

// Format bar value for the label shown above each bar. Values ≥ 1000 are abbreviated, e.g., 1.25k.
const formatBarLabel = (value) => {
  if (value >= 1000) {
    // Keep up to two decimals, strip trailing zeros
    const formatted = (value / 1000)
      .toFixed(2)
      .replace(/\.0+$|0+$/,'');
    return `$${formatted}k`;
  }
  return `$${value}`;
};

const HSAChart = forwardRef(({ data, loading, defaultPeriod = Period.week, initialWeek = null, selectedYear: propSelectedYear, onYearChange, onWeekChange }, ref) => {
  // Initialize all state values
  const [chartPeriod, setChartPeriod] = useState(defaultPeriod || Period.week);
  const [barData, setBarData] = useState([]);
  const [currentDate, setCurrentDate] = useState(() => {
    // Initialize with current date or provided initial week
    return initialWeek ? new Date(initialWeek) : new Date();
  });
  const [chartKey, setChartKey] = useState(0);
  const [transactionType, setTransactionType] = useState("Expense");
  const [totalAmount, setTotalAmount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedYear, setSelectedYear] = useState(propSelectedYear || new Date().getFullYear());
  const [yearPickerVisible, setYearPickerVisible] = useState(false);
  const [allYears, setAllYears] = useState([]);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const totalAmountAnim = useRef(new Animated.Value(0)).current;
  const [displayedTotal, setDisplayedTotal] = useState(0);

  // Simplified data loading without complex caching
  const isLoadingRef = useRef(false);
  const isYearChangingRef = useRef(false);



  // Use useLayoutEffect for all initial state setup that might cause updates
  useLayoutEffect(() => {
    // Set initial values for animations
    totalAmountAnim.setValue(totalAmount);
    setDisplayedTotal(totalAmount);
  }, [totalAmount]);



  // Simplified data loading function
  const loadData = async (period, date, type) => {
    // Prevent multiple simultaneous loads
    if (isLoadingRef.current) {
      return;
    }

    isLoadingRef.current = true;
    setIsLoading(true);

    try {
      let fetchedData;
      let processedData;
      let totalValue = 0;

      if (period === Period.week) {
        const { startDate, endDate } = getWeekRange(date);
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        console.log(`HSAChart: Loading weekly data for year ${date.getFullYear()}, week: ${startDateObj.toISOString()} to ${endDateObj.toISOString()}`);
        fetchedData = await transactionService.getWeeklyChartData(startDate, endDate, type);
        processedData = processWeeklyData(fetchedData, type);

        // Calculate total for the week
        if (fetchedData && fetchedData.length > 0) {
          totalValue = fetchedData.reduce((sum, item) => sum + (item.total || 0), 0);
        }
      } else if (period === Period.year) {
        const year = date.getFullYear();
        console.log(`HSAChart: Loading yearly data for year ${year}`);
        fetchedData = await transactionService.getYearlyChartData(year, type);
        processedData = processYearlyData(fetchedData, type);
        totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
      }

      // Update the chart data
      setBarData(processedData);
      setTotalAmount(totalValue);
      setChartKey(prev => prev + 1);

    } catch (error) {
      console.error('Error loading data:', error);
      setBarData([]);
      setTotalAmount(0);
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  // Consolidated effect for data loading - only load when essential parameters change
  useEffect(() => {
    console.log(`🔄 Data loading useEffect triggered - isYearChanging: ${isYearChangingRef.current}, chartPeriod: ${chartPeriod}, transactionType: ${transactionType}, currentDate: ${currentDate?.toISOString()}, selectedYear: ${selectedYear}, propSelectedYear: ${propSelectedYear}`);

    if (chartPeriod && transactionType && currentDate && selectedYear) {
      // Don't load data if a year change is in progress
      if (isYearChangingRef.current) {
        console.log(`⏳ Year change in progress, skipping data load`);
        return;
      }

      // Ensure the currentDate year matches the selectedYear before loading data
      const currentDateYear = currentDate.getFullYear();
      if (currentDateYear === selectedYear) {
        console.log(`✅ Date and year are synchronized, loading data`);
        console.log(`Loading data for ${chartPeriod} view, type: ${transactionType}, date: ${currentDate.toISOString()}, year: ${currentDate.getFullYear()}`);
        loadData(chartPeriod, currentDate, transactionType);
      } else {
        console.log(`⏳ Waiting for date sync - currentDate year: ${currentDateYear}, selectedYear: ${selectedYear}`);
      }
    }
  }, [chartPeriod, transactionType, currentDate, selectedYear]);

  // Load available years on component mount
  useEffect(() => {
    loadAvailableYears();
  }, []);

  // Handle year changes from parent component
  useEffect(() => {
    console.log(`HSAChart: useEffect triggered - propSelectedYear: ${propSelectedYear}, selectedYear: ${selectedYear}`);
    if (propSelectedYear && propSelectedYear !== selectedYear) {
      console.log(`HSAChart: Year changed from prop: ${propSelectedYear}`);

      // Set flag to prevent data loading during year change
      isYearChangingRef.current = true;

      setSelectedYear(propSelectedYear);

      // Update the current date to reflect the new year
      // Keep the same month and day, but change the year
      const newDate = new Date(currentDate);
      console.log(`HSAChart: Current date before update: ${currentDate.toISOString()}`);
      newDate.setFullYear(propSelectedYear);
      console.log(`HSAChart: New date after setFullYear: ${newDate.toISOString()}`);

      // If the new date would be in the future (for current year), use today's date
      const today = new Date();
      const finalDate = (propSelectedYear === today.getFullYear() && newDate > today) ? today : newDate;

      setCurrentDate(finalDate);
      console.log(`HSAChart: Updated currentDate to: ${finalDate.toISOString()}`);

      // Load data immediately with the correct date to avoid timing issues
      if (chartPeriod && transactionType) {
        console.log(`HSAChart: Loading data immediately with new date: ${finalDate.toISOString()}`);
        loadData(chartPeriod, finalDate, transactionType);
      }

      // Clear the flag after a longer delay to allow state updates to complete
      setTimeout(() => {
        isYearChangingRef.current = false;
        console.log(`HSAChart: Year change completed, data loading re-enabled`);
      }, 500);
    }
  }, [propSelectedYear]);





  const loadAvailableYears = async () => {
    try {
      const years = await transactionService.getAvailableYears();
      const currentYear = new Date().getFullYear();

      if (years && years.length > 0) {
        // Sort years in descending order
        years.sort((a, b) => b - a);

        // Make sure current year is included
        if (!years.includes(currentYear.toString()) && !years.includes(currentYear)) {
          years.unshift(currentYear);
        }

        setAllYears(years);
      } else {
        // If no years available, use current year
        setAllYears([currentYear]);
      }
    } catch (error) {
      console.error('Error loading available years:', error);
      // Fallback to current year if API fails
      const currentYear = new Date().getFullYear();
      setAllYears([currentYear]);
    }
  };



  const calculateTotal = (data) => {
    const sum = data.reduce((total, item) => total + item.value, 0);
    setTotalAmount(sum);
  };

  const getWeekRange = (date) => {
    // Create a new date object to avoid modifying the original
    const currentDate = new Date(date);
    
    // Get the current day of the week (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeek = currentDate.getDay();
    
    // Calculate the date of the Sunday that starts this week
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - dayOfWeek);
    startOfWeek.setHours(0, 0, 0, 0); // Set to beginning of the day
    
    // Calculate the date of the Saturday that ends this week
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999); // Set to end of the day
    
    return {
      startDate: startOfWeek.getTime(),
      endDate: endOfWeek.getTime(),
    };
  };

  const getMonthRange = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const startOfMonth = new Date(year, month, 1);
    const endOfMonth = new Date(year, month + 1, 0);

    return {
      startDate: startOfMonth.getTime(),
      endDate: endOfMonth.getTime(),
    };
  };

  const handlePrevious = () => {
    if (chartPeriod === Period.week) {
      // Navigate to previous week - allow crossing year boundaries for proper calendar logic
      const newDate = new Date(currentDate);
      newDate.setDate(currentDate.getDate() - 7);

      setCurrentDate(newDate);

      // Update selected year if we crossed into a different year
      const newYear = newDate.getFullYear();
      if (newYear !== selectedYear) {
        setSelectedYear(newYear);
        // Notify parent component about year change
        if (onYearChange && typeof onYearChange === 'function') {
          onYearChange(newYear);
        }
      }

      // Notify parent component about week change
      if (onWeekChange && typeof onWeekChange === 'function') {
        onWeekChange(newDate);
      }
    } else if (chartPeriod === Period.year) {
      // Navigate to previous year
      const newYear = selectedYear - 1;
      setSelectedYear(newYear);

      const newDate = new Date(currentDate);
      newDate.setFullYear(newYear);
      setCurrentDate(newDate);

      // Notify parent component about year change
      if (onYearChange && typeof onYearChange === 'function') {
        onYearChange(newYear);
      }
    }
  };

  const handleNext = () => {
    if (chartPeriod === Period.week) {
      // Navigate to next week - allow crossing year boundaries for proper calendar logic
      const newDate = new Date(currentDate);
      newDate.setDate(currentDate.getDate() + 7);

      // Don't allow navigating beyond current week if we're in the current week
      const today = new Date();
      const currentWeekStart = new Date(getWeekRange(today).startDate);
      const newWeekStart = new Date(getWeekRange(newDate).startDate);

      // Allow navigation if we're not going beyond the current week
      if (newWeekStart <= currentWeekStart) {
        setCurrentDate(newDate);

        // Update selected year if we crossed into a different year
        const newYear = newDate.getFullYear();
        if (newYear !== selectedYear) {
          setSelectedYear(newYear);
          // Notify parent component about year change
          if (onYearChange && typeof onYearChange === 'function') {
            onYearChange(newYear);
          }
        }

        // Notify parent component about week change
        if (onWeekChange && typeof onWeekChange === 'function') {
          onWeekChange(newDate);
        }
      }
    } else if (chartPeriod === Period.year) {
      // Navigate to next year
      const today = new Date();
      const newYear = selectedYear + 1;

      // Don't allow navigating beyond current year
      if (newYear <= today.getFullYear()) {
        setSelectedYear(newYear);

        const newDate = new Date(currentDate);
        newDate.setFullYear(newYear);
        setCurrentDate(newDate);

        // Notify parent component about year change
        if (onYearChange && typeof onYearChange === 'function') {
          onYearChange(newYear);
        }
      }
    }
  };

  // Handle swipe gestures
  const handleSwipeGesture = (event) => {
    const { translationX, translationY, velocityX, velocityY, state } = event.nativeEvent;
    
    if (state === State.END) {
      // Check if this is primarily a horizontal gesture
      const isHorizontalGesture = Math.abs(translationX) > Math.abs(translationY);
      const isSignificantHorizontalMovement = Math.abs(translationX) > 50;
      const isSignificantHorizontalVelocity = Math.abs(velocityX) > 500;
      
      // Only trigger navigation if it's clearly a horizontal swipe
      if (isHorizontalGesture && (isSignificantHorizontalMovement || isSignificantHorizontalVelocity)) {
        if (translationX > 0) {
          // Swipe right - go to previous period
          handlePrevious();
        } else {
          // Swipe left - go to next period
          handleNext();
        }
      }
    }
  };

  const processWeeklyData = (data, type) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Get the week range to display actual dates
    const weekRange = getWeekRange(currentDate);
    const weekStart = new Date(weekRange.startDate);

    // Create default data structure with all days initialized to 0
    const defaultData = days.map((day, index) => {
      const roundedValue = 0;

      // Calculate the actual date for this day of the week
      const dayDate = new Date(weekStart);
      dayDate.setDate(weekStart.getDate() + index);

      // Create a label with day name
      const dayLabel = day;

      return {
        value: roundedValue,
        label: dayLabel,
        frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
        dayIndex: index,
        dayDate: dayDate,
        topLabelComponent: () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        )
      };
    });

    // If no data is returned, use the default structure
    if (!data || data.length === 0) {
      return defaultData;
    }

    // Fill in actual values where they exist
    data.forEach(item => {
      if (item.dayOfWeek >= 0 && item.dayOfWeek < 7) {
        const roundedValue = Math.round(item.total);
        defaultData[item.dayOfWeek].value = roundedValue;
        defaultData[item.dayOfWeek].topLabelComponent = () => (
          <View style={{ paddingTop: 4, minHeight: 30, alignItems: 'center' }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        );
      }
    });

    return defaultData;
  };

  const processMonthlyData = (data, type) => {
    if (!data || data.length === 0) {
      // Generate default data for 12 months if no data is available
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return monthNames.map(month => ({
        value: 0,
        label: month,
        frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
      }));
    }

    // Show all 12 months of data (full year)
    return data.map(item => ({
      value: type === 'Expense' ? (item.expenses || 0) : (item.contributions || 0),
      label: item.month,
      frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
      gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
    }));
  };

  const processYearlyData = (data, type) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Create default data structure with all months initialized to 0
    const defaultData = monthNames.map(month => ({
      value: 0,
      label: month,
      frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
      gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
    }));
    
    // If no data is returned, use the default structure
    if (!data || data.length === 0) {
      return defaultData;
    }
    
    // Fill in actual values where they exist
    data.forEach(item => {
      const monthIndex = parseInt(item.month) - 1; // Convert month (1-12) to array index (0-11)
      if (monthIndex >= 0 && monthIndex < 12) {
        const value = type === 'Expense' ? (item.expenses || 0) : (item.contributions || 0);
        const roundedValue = Math.round(value);
        defaultData[monthIndex].value = roundedValue;
        defaultData[monthIndex].topLabelComponent = () => (
          <View style={{ paddingTop: 4, minHeight: 30, alignItems: 'center' }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        );
      }
    });
    
    return defaultData;
  };

  const getPeriodTitle = () => {
    if (chartPeriod === Period.week) {
      // Get the current week's Sunday and Saturday
      const weekRange = getWeekRange(currentDate);
      const startDate = new Date(weekRange.startDate);
      const endDate = new Date(weekRange.endDate);

      // Format dates properly
      const startMonth = startDate.toLocaleDateString("en-US", { month: "short" });
      const endMonth = endDate.toLocaleDateString("en-US", { month: "short" });
      const startDay = startDate.getDate();
      const endDay = endDate.getDate();
      const year = startDate.getFullYear();

      // Handle case where week spans two different months
      if (startMonth === endMonth) {
        return `${startMonth} ${startDay}-${endDay}, ${year}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
      }
    } else if (chartPeriod === Period.month) {
      return currentDate.toLocaleDateString("en-US", { month: "long", year: "numeric" });
    } else if (chartPeriod === Period.year) {
      return currentDate.getFullYear().toString();
    }
    return "";
  };

  // Generate years from 1950 to 2099
  const generateAllYears = () => {
    const years = [];
    for (let year = 1950; year <= 2099; year++) {
      years.push(year);
    }
    return years;
  };

  // Handle year selection
  const handleYearChange = (year) => {
    console.log(`HSAChart: Changing year to ${year}`);

    // Update the state
    setSelectedYear(year);
    setYearPickerVisible(false);

    // For year view, just update the year. For week view, preserve the week but change year
    if (chartPeriod === Period.year) {
      // For year view, create a date in the selected year
      const newDate = new Date();
      newDate.setFullYear(year);
      setCurrentDate(newDate);
    } else if (chartPeriod === Period.week) {
      // For week view, try to preserve the same week but in the new year
      const newDate = new Date(currentDate);
      newDate.setFullYear(year);
      setCurrentDate(newDate);
    }

    // Notify parent component about year change
    if (onYearChange && typeof onYearChange === 'function') {
      onYearChange(year);
    }
  };

  // Year picker modal
  const renderYearPickerModal = () => (
    <Modal
      visible={yearPickerVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setYearPickerVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.yearPickerContainer}>
          <View style={styles.yearPickerHeader}>
            <Text style={styles.yearPickerTitle}>Select Year</Text>
            <TouchableOpacity 
              style={styles.closeButtonContainer}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.yearPickerContent}>
            {/* Year grid - use available years from API instead of decades */}
            <View style={styles.yearGrid}>
              {allYears.map(year => (
                <TouchableOpacity
                  key={year}
                  style={[
                    styles.yearGridItem,
                    selectedYear === year && styles.yearGridItemSelected
                  ]}
                  onPress={() => handleYearChange(year)}
                >
                  <Text
                    style={[
                      styles.yearGridItemText,
                      selectedYear === year && styles.yearGridItemTextSelected,
                      year === new Date().getFullYear() && styles.currentYearText
                    ]}
                  >
                    {year}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.yearPickerActions}>
            <TouchableOpacity
              style={styles.yearPickerButton}
              onPress={() => handleYearChange(new Date().getFullYear())}
            >
              <Text style={styles.yearPickerButtonText}>Current Year</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.yearPickerButton, styles.yearPickerPrimaryButton]}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={[styles.yearPickerButtonText, styles.yearPickerPrimaryButtonText]}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Expose a method to set the chart to the current week
  useImperativeHandle(ref, () => ({
    setToCurrentWeek: () => {
      setChartPeriod(Period.week);
      const today = new Date();
      setCurrentDate(today);
      setSelectedYear(today.getFullYear());
    },

    // Add a new method to set a specific week
    setToSpecificWeek: (date) => {
      setChartPeriod(Period.week);
      const newDate = new Date(date);
      setCurrentDate(newDate);
      setSelectedYear(newDate.getFullYear());
    }
  }));

  // Initialize with the specified period and date - only run once on mount
  useEffect(() => {
    if (defaultPeriod) {
      setChartPeriod(defaultPeriod);
    }

    // Initialize with the provided initialWeek or today's date
    const initialDate = initialWeek ? new Date(initialWeek) : new Date();
    setCurrentDate(initialDate);

    // Set the selected year based on the initial date
    if (propSelectedYear) {
      setSelectedYear(propSelectedYear);
    } else {
      setSelectedYear(initialDate.getFullYear());
    }
  }, []); // Empty dependency array - only run on mount

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>
        
        <SegmentedControl
          values={["Week", "Year"]}
          style={styles.segmentedControl}
          selectedIndex={chartPeriod === Period.week ? 0 : 1}
          onChange={(event) => {
            const index = event.nativeEvent.selectedSegmentIndex;
            setChartPeriod(index === 0 ? Period.week : Period.year);
          }}
        />
        
        <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
        <Text style={styles.periodSubtitle}>
          Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
        </Text>

        <Text style={styles.totalAmount}>$0.00</Text>
        
        <View style={styles.chartContainer}>
          <BarChart
            data={Array(7).fill().map((_, i) => ({
              value: 0,
              label: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i],
              frontColor: '#e5e7eb',
              gradientColor: '#d1d5db',
              dayIndex: i,
            }))}
            barWidth={chartPeriod === Period.week ? 24 : 18}
            height={220}
            width={SCREEN_WIDTH - 40}
            minHeight={3}
            barBorderRadius={4}
            showGradient
            spacing={chartPeriod === Period.week ? 18 : chartPeriod === Period.month ? 20 : 9}
            noOfSections={4}
            yAxisThickness={0}
            xAxisThickness={0}
            xAxisLabelsVerticalShift={5}
            xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
            yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
            horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
          />
          <View style={styles.loadingOverlay}>
            <Text style={styles.loadingText}>Loading chart data...</Text>
          </View>
        </View>
        
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            disabled={true}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.left.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"#d1d5db"}
            />
            <Text style={[styles.controlButtonText, {color: '#d1d5db'}]}>Prev {chartPeriod}</Text>
          </TouchableOpacity>
          
          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />
          
          <TouchableOpacity
            disabled={true}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.right.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"#d1d5db"}
            />
            <Text style={[styles.controlButtonText, {color: '#d1d5db'}]}>Next {chartPeriod}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (chartPeriod === Period.year) {
    return (
      <PanGestureHandler
        onGestureEvent={handleSwipeGesture}
        onHandlerStateChange={handleSwipeGesture}
        activeOffsetX={[-20, 20]}
        failOffsetY={[-20, 20]}
        shouldCancelWhenOutside={true}
      >
        <View style={styles.container}>
          <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>
          
          <SegmentedControl
            values={["Week", "Year"]}
            style={styles.segmentedControl}
            selectedIndex={chartPeriod === Period.week ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setChartPeriod(index === 0 ? Period.week : Period.year);
            }}
          />
          
          <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
          <Text style={styles.periodSubtitle}>
            Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
          </Text>

          <Animated.Text style={[styles.totalAmount, { opacity: fadeAnim }]}>
            ${displayedTotal.toFixed(2)}
          </Animated.Text>
          
          <View style={styles.swipeHintContainer}>
            <Text style={styles.swipeHintText}>← Swipe to navigate →</Text>
          </View>
          
          <View style={styles.chartOuterContainer}>
            <Animated.View 
              style={[
                styles.chartContainer,
                { 
                  opacity: fadeAnim,
                  transform: [{ scale: scaleAnim }]
                }
              ]}
            >
              <BarChart
                key={chartKey}
                data={barData}
                barWidth={18}
                height={220}
                width={SCREEN_WIDTH - 40}
                minHeight={3}
                barBorderRadius={4}
                showGradient
                spacing={10}
                noOfSections={4}
                yAxisThickness={0}
                xAxisThickness={0}
                xAxisLabelsVerticalShift={5}
                xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
                yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
                horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
                xAxisLabelComponent={(item) => (
                  <Text style={{ color: "gray", fontSize: 12 }}>{item.label}</Text>
                )}
                isAnimated
                animationDuration={600}
                topLabelContainerStyle={{ height: 30 }}
                scrollToEnd={false}
                disableScroll={false}
                leftShiftForLastIndexToFit={true}
                formatYLabel={(value) => value.toLocaleString()}
                maxValue={(() => {
                  const NUMBER_OF_SECTIONS = 4;
                  const maxVal = Math.max(...barData.map(item => item.value), 0);
                  
                  let roundedMax;
                  
                  if (maxVal <= 100) {
                    roundedMax = Math.ceil(maxVal / 25) * 25;
                  } else if (maxVal <= 1000) {
                    roundedMax = Math.ceil(maxVal / 100) * 100;
                  } else if (maxVal <= 10000) {
                    roundedMax = Math.ceil(maxVal / 500) * 500;
                  } else {
                    roundedMax = Math.ceil(maxVal / 1000) * 1000;
                  }
                  
                  const remainder = roundedMax % NUMBER_OF_SECTIONS;
                  if (remainder !== 0) {
                    roundedMax = roundedMax + (NUMBER_OF_SECTIONS - remainder);
                  }
                  
                  const sectionSize = roundedMax / NUMBER_OF_SECTIONS;
                  return roundedMax + sectionSize;
                })()}
              />
            </Animated.View>
            

          </View>
          
          <View style={styles.controlsContainer}>
            <TouchableOpacity
              onPress={handlePrevious}
              style={styles.controlButton}
            >
              <SymbolView
                name="chevron.left.circle.fill"
                size={40}
                type="hierarchical"
                tintColor={"gray"}
              />
              <Text style={styles.controlButtonText}>Prev {chartPeriod}</Text>
            </TouchableOpacity>
            
            <SegmentedControl
              values={["Expense", "Contribution"]}
              style={styles.typeSegmentedControl}
              selectedIndex={transactionType === "Expense" ? 0 : 1}
              onChange={(event) => {
                const index = event.nativeEvent.selectedSegmentIndex;
                setTransactionType(index === 0 ? "Expense" : "Contribution");
              }}
            />
            
            <TouchableOpacity
              onPress={handleNext}
              style={styles.controlButton}
            >
              <SymbolView
                name="chevron.right.circle.fill"
                size={40}
                type="hierarchical"
                tintColor={"gray"}
              />
              <Text style={styles.controlButtonText}>Next {chartPeriod}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </PanGestureHandler>
    );
  }

  return (
    <PanGestureHandler
      onGestureEvent={handleSwipeGesture}
      onHandlerStateChange={handleSwipeGesture}
      activeOffsetX={[-20, 20]}
      failOffsetY={[-20, 20]}
      shouldCancelWhenOutside={true}
    >
      <View style={styles.container}>
        <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>
        
        <SegmentedControl
          values={["Week", "Year"]}
          style={styles.segmentedControl}
          selectedIndex={chartPeriod === Period.week ? 0 : 1}
          onChange={(event) => {
            const index = event.nativeEvent.selectedSegmentIndex;
            setChartPeriod(index === 0 ? Period.week : Period.year);
          }}
        />
        
        <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
        <Text style={styles.periodSubtitle}>
          Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
        </Text>

        <Animated.Text style={[styles.totalAmount, { opacity: fadeAnim }]}>
          ${displayedTotal.toFixed(2)}
        </Animated.Text>
        
        <View style={styles.swipeHintContainer}>
          <Text style={styles.swipeHintText}>← Swipe to navigate →</Text>
        </View>
        
        <View style={styles.chartOuterContainer}>
          <Animated.View 
            style={[
              styles.chartContainer,
              { 
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <BarChart
              key={chartKey}
              data={barData}
              barWidth={chartPeriod === Period.week ? 22 : 16}
              height={240}
              width={SCREEN_WIDTH - 60}
              minHeight={3}
              barBorderRadius={4}
              showGradient
              spacing={chartPeriod === Period.week ? 20 : chartPeriod === Period.month ? 22 : 11}
              noOfSections={4}
              yAxisThickness={0}
              xAxisThickness={0}
              xAxisLabelsVerticalShift={8}
              xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
              yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 4 }}
              topLabelContainerStyle={{ height: 40, paddingBottom: 5 }}
              initialSpacing={chartPeriod === Period.week ? 20 : 10}
              endSpacing={chartPeriod === Period.week ? 20 : 10}
              horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
              xAxisLabelComponent={(item) => {
                if (chartPeriod === Period.week && item.dayDate) {
                  return (
                    <View style={{ alignItems: 'center', width: 40, paddingHorizontal: 2 }}>
                      <Text style={{ color: "#666", fontSize: 11, fontWeight: '500' }}>{item.label}</Text>
                      <Text style={{ color: "#666", fontSize: 13, fontWeight: '600', marginTop: 3 }}>{item.dayDate.getDate()}</Text>
                    </View>
                  );
                }
                return <Text style={{ color: "gray", fontSize: 12 }}>{item.label}</Text>;
              }}
              isAnimated
              animationDuration={600}
              topLabelContainerStyle={{ height: 30 }}
              scrollToEnd={false}
              disableScroll={chartPeriod === Period.week}
              initialSpacing={chartPeriod === Period.week ? 16 : undefined}
              leftShiftForLastIndexToFit={chartPeriod === Period.week ? true : undefined}
              formatYLabel={(value) => {
                return value.toLocaleString();
              }}
              maxValue={(() => {
                const NUMBER_OF_SECTIONS = 4;
                const maxVal = Math.max(...barData.map(item => item.value), 0);
                
                let roundedMax;
                
                if (maxVal <= 100) {
                  roundedMax = Math.ceil(maxVal / 25) * 25;
                } else if (maxVal <= 1000) {
                  roundedMax = Math.ceil(maxVal / 100) * 100;
                } else if (maxVal <= 10000) {
                  roundedMax = Math.ceil(maxVal / 500) * 500;
                } else {
                  roundedMax = Math.ceil(maxVal / 1000) * 1000;
                }
                
                const remainder = roundedMax % NUMBER_OF_SECTIONS;
                if (remainder !== 0) {
                  roundedMax = roundedMax + (NUMBER_OF_SECTIONS - remainder);
                }
                
                const sectionSize = roundedMax / NUMBER_OF_SECTIONS;
                return roundedMax + sectionSize;
              })()}
            />
          </Animated.View>
          

        </View>
        
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            onPress={handlePrevious}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.left.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"gray"}
            />
            <Text style={styles.controlButtonText}>Prev {chartPeriod}</Text>
          </TouchableOpacity>
          
          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />
          
          <TouchableOpacity
            onPress={handleNext}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.right.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"gray"}
            />
            <Text style={styles.controlButtonText}>Next {chartPeriod}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </PanGestureHandler>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
  },
  segmentedControl: {
    marginBottom: 16,
  },
  yearSegmentedControl: {
    marginBottom: 16,
    width: 200
  },
  periodTitle: {
    fontWeight: "700", 
    fontSize: 18, 
    marginBottom: 8
  },
  periodSubtitle: {
    color: "gray"
  },
  totalAmount: {
    fontWeight: "700", 
    fontSize: 32, 
    marginBottom: 16
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "baseline",
    marginTop: 16,
  },
  controlButton: {
    alignItems: "center"
  },
  controlButtonText: {
    fontSize: 11,
    color: "gray"
  },
  typeSegmentedControl: {
    width: 200
  },
  loadingContainer: {
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  barValueLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    paddingHorizontal: 2,
    minWidth: 40,
  },
  swipeHintContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  swipeHintText: {
    fontSize: 14,
    color: 'gray',
  },
  yearViewContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  yearSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  yearSelectorText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginRight: 8,
  },
  yearSelectorIcon: {
    fontSize: 12,
    color: '#64748B',
  },
  summaryCard: {
    backgroundColor: '#f7f7f7',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333',
  },
  chartCard: {
    backgroundColor: '#f7f7f7',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    position: 'relative',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  chartContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingTop: 20,
    paddingBottom: 10,
    overflow: 'hidden',
  },
  chartOuterContainer: {
    position: 'relative',
  },
  fullLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    zIndex: 10,
    borderRadius: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearPickerContainer: {
    width: SCREEN_WIDTH * 0.85,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  yearPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  yearPickerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A2138',
  },
  closeButtonContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    fontSize: 18,
    color: '#64748B',
    fontWeight: '600',
  },
  yearPickerContent: {
    padding: 16,
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  yearGridItem: {
    width: '20%',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  yearGridItemSelected: {
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
  },
  yearGridItemText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#64748B',
  },
  yearGridItemTextSelected: {
    color: '#4C6FFF',
    fontWeight: '700',
  },
  currentYearText: {
    textDecorationLine: 'underline',
  },
  yearPickerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  yearPickerButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  yearPickerPrimaryButton: {
    backgroundColor: '#4C6FFF',
  },
  yearPickerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748B',
  },
  yearPickerPrimaryButtonText: {
    color: '#FFFFFF',
  },
});

export default HSAChart;
