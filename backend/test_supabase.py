#!/usr/bin/env python3

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_supabase_connection():
    url = os.getenv("SUPABASE_URL")
    anon_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    service_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    print(f"Supabase URL: {url}")
    print(f"Anon Key: {anon_key[:20] if anon_key else 'Not set'}...")
    print(f"Service Key: {service_key[:20] if service_key else 'Not set'}...")
    
    if not url or not anon_key:
        print("❌ Missing Supabase configuration!")
        return False
    
    # Test connection to hsa_documents table
    endpoint = f"{url}/rest/v1/hsa_documents"
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(endpoint, headers=headers)
        print(f"Table check response: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            print("✅ hsa_documents table exists and is accessible")
            return True
        else:
            print(f"❌ Error accessing hsa_documents table: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Connection error: {str(e)}")
        return False

def test_storage_bucket():
    url = os.getenv("SUPABASE_URL")
    anon_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    # Test storage bucket
    endpoint = f"{url}/storage/v1/bucket/hsa-documents"
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
    }
    
    try:
        response = requests.get(endpoint, headers=headers)
        print(f"Storage bucket check response: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            print("✅ hsa-documents storage bucket exists")
            return True
        else:
            print(f"❌ Error accessing storage bucket: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Storage connection error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Supabase connection...")
    test_supabase_connection()
    print("\nTesting storage bucket...")
    test_storage_bucket()
