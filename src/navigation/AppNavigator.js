import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Text, View, ActivityIndicator, StyleSheet, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import AuthNavigator from './AuthNavigator';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import BenefitDetailsScreen from '../screens/BenefitDetailsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import HSAManagementScreen from '../screens/HSAManagementScreen';
import HSADocumentsScreen from '../screens/HSADocumentsScreen';
import HSADocumentDetailScreen from '../screens/HSADocumentDetailScreen';
import HSAAllTransactionsScreen from '../screens/HSAAllTransactionsScreen';
import HSAContributionHistoryScreen from '../screens/HSAContributionHistoryScreen';
import UploadReceiptScreen from '../screens/UploadReceiptScreen';
import SpendingVisualizationScreen from '../screens/SpendingVisualizationScreen';
import DocumentViewerScreen from '../screens/DocumentViewerScreen';

// Create navigators
const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

const styles = StyleSheet.create({
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 56,
    width: '100%', // Add full width
    minWidth: 80, // Add minimum width
  },
  iconBackground: {
    width: 48,
    height: 48,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
});

const TabIcon = ({ focused, name }) => {
  const iconMap = {
    Home: 'home',
    HSA: 'credit-card',
    Profile: 'user'
  };

  const scale = useSharedValue(0.9);

  React.useEffect(() => {
    scale.value = withSpring(focused ? 1 : 0.9, {
      mass: 1,
      damping: 15,
      stiffness: 120
    });
  }, [focused]);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }]
    };
  });

  return (
    <View style={styles.tabIconContainer}>
      <Animated.View
        style={[
          styles.iconBackground,
          animatedStyles,
          { backgroundColor: focused ? '#4C6FFF' : 'transparent' }
        ]}
      >
        <Feather
          name={iconMap[name]}
          size={22}
          color={focused ? '#FFFFFF' : '#94A3B8'}
        />
      </Animated.View>
    </View>
  );
};

// Home stack navigator
const HomeStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="HomeScreen" 
        component={HomeScreen} 
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="BenefitDetails" 
        component={BenefitDetailsScreen} 
        options={{ 
          headerTitle: 'Benefit Details',
          headerTintColor: '#fff',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          }
        }}
      />
    </Stack.Navigator>
  );
}

// HSA stack navigator
const HSAStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="HSAManagement"
        component={HSAManagementScreen}
        options={{
          title: 'HSA Management',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <Stack.Screen
        name="HSADocuments"
        component={HSADocumentsScreen}
        options={{
          title: 'HSA Documents',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <Stack.Screen
        name="HSAAllTransactions"
        component={HSAAllTransactionsScreen}
        options={{
          title: 'All Transactions',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <Stack.Screen
        name="UploadReceipt"
        component={UploadReceiptScreen}
        options={{
          title: 'Upload Receipt',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <Stack.Screen
        name="Spending"
        component={SpendingVisualizationScreen}
        options={{
          title: 'Spending',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <Stack.Screen 
        name="DocumentViewerScreen" 
        component={DocumentViewerScreen} 
        options={{ 
          headerShown: false
        }}
      />
      <Stack.Screen
        name="HSADocumentDetail"
        component={HSADocumentDetailScreen}
        options={{
          headerShown: false
        }}
      />
      <Stack.Screen
        name="HSAContributionHistory"
        component={HSAContributionHistoryScreen}
        options={{
          title: 'Contribution History',
          headerStyle: {
            backgroundColor: '#4a6fa5',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
    </Stack.Navigator>
  );
}

// Main tab navigator
const MainAppNavigator = () => {
  return (
    <Tab.Navigator
      initialRouteName="HSA"
      screenOptions={{
        tabBarActiveTintColor: '#4C6FFF',
        tabBarInactiveTintColor: '#94A3B8',
        tabBarStyle: {
          height: 80,
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: 'rgba(0, 0, 0, 0.05)',
          paddingTop: 8,
          paddingBottom: 16,
        },
        tabBarShowLabel: true,
        tabBarHideOnKeyboard: true,
        tabBarAllowFontScaling: true,
        headerStyle: {
          backgroundColor: '#FFFFFF',
          elevation: 0,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.05,
          shadowRadius: 8,
        },
        headerTintColor: '#1A2138',
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        }
      }}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeStack} 
        options={{
          tabBarIcon: ({ focused }) => <TabIcon focused={focused} name="Home" />,
          headerShown: false,
        }}
      />
      <Tab.Screen 
        name="HSA" 
        component={HSAStack} 
        options={{
          tabBarIcon: ({ focused }) => <TabIcon focused={focused} name="HSA" />,
          headerShown: false,
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{
          tabBarIcon: ({ focused }) => <TabIcon focused={focused} name="Profile" />,
          headerTitle: 'My Profile',
        }}
      />
    </Tab.Navigator>
  );
};

// Root navigator with auth condition
const AppNavigator = ({ navigationRef }) => {
  const { isAuthenticated, loading } = useSupabaseAuth();

  // Show loading indicator while checking auth state
  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8f9fa' }}>
        <ActivityIndicator size="large" color="#4a6fa5" />
      </View>
    );
  }

  return (
    <NavigationContainer ref={navigationRef}>
      {isAuthenticated ? <MainAppNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

export default AppNavigator;
