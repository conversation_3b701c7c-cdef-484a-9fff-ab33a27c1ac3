import google.generativeai as genai
import base64
import json
import os
from datetime import datetime
from PIL import Image
import io

class GeminiProcessor:
    def __init__(self):
        # Initialize Gemini API
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
    def process_receipt_image(self, image_data):
        """
        Process receipt image using Gemini 1.5 Flash multimodal capabilities
        """
        try:
            print("\n===== GEMINI PROCESSING START =====")
            
            # Convert image data to PIL Image
            image = Image.open(io.BytesIO(image_data))
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            print(f"Processing image: {image.size} pixels, mode: {image.mode}")
            
            # Create the prompt for receipt analysis
            prompt = self._create_receipt_prompt()
            
            print("Sending image to Gemini 1.5 Flash...")
            
            # Send image and prompt to Gemini
            response = self.model.generate_content([prompt, image])
            
            print("Gemini response received")
            print(f"Raw response: {response.text}")
            
            # Parse the JSON response
            extracted_data = self._parse_gemini_response(response.text)
            
            print("\n===== GEMINI EXTRACTION RESULTS =====")
            for key, value in extracted_data.items():
                print(f"{key}: {value}")
            print("===== GEMINI PROCESSING COMPLETE =====\n")
            
            return {
                'success': True,
                'extracted_data': extracted_data,
                'confidence': 'high',  # Gemini typically has high confidence
                'raw_response': response.text,
                'method': 'gemini'
            }
            
        except Exception as e:
            print(f"Gemini processing error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'method': 'gemini'
            }
    
    def _create_receipt_prompt(self):
        """
        Create a detailed prompt for receipt analysis
        """
        return """
Analyze this receipt/medical expense image and extract the following information in JSON format:

{
  "vendor": "Name of the business/provider (e.g., CVS Pharmacy, Dr. Smith's Office)",
  "amount": 12.34,
  "expense_date": "YYYY-MM-DD",
  "category": "One of: Medical, Pharmacy, Dental, Vision, Mental Health, Alternative Medicine",
  "title": "Brief descriptive title (e.g., 'Prescription - CVS Pharmacy')",
  "description": "More detailed description of the expense",
  "confidence_notes": "Any uncertainty or notes about the extraction"
}

Instructions:
1. Look for the business name, provider name, or vendor at the top of the receipt
2. Find the total amount paid (look for "Total", "Amount Due", "Balance", etc.)
3. Extract the date of service or transaction date
4. Categorize based on the type of medical expense:
   - Medical: General medical services, doctor visits, hospital bills
   - Dental: Dental services, orthodontics, oral surgery
   - Vision: Eye exams, glasses, contacts, ophthalmology
   - Prescription: Prescription drugs, medications provided by pharmacy
   - Pharmacy: Prescription medications, over-the-counter medicines, pharmacy purchases
   - Therapy: Physical therapy, occupational therapy, speech therapy, counseling
   - Family: Expenses covering multiple family members or dependents
   - Other: Any expense not classified above; provide a brief description
5. Create a clear title and description
6. If any information is unclear or missing, note it in confidence_notes

Return ONLY the JSON object, no additional text or formatting.
"""
    
    def _parse_gemini_response(self, response_text):
        """
        Parse Gemini's JSON response and return structured data
        """
        try:
            # Clean the response text (remove any markdown formatting)
            cleaned_text = response_text.strip()
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()
            
            # Parse JSON
            data = json.loads(cleaned_text)
            
            # Validate and format the data
            return {
                'title': data.get('title', 'Medical Expense'),
                'description': data.get('description', 'Medical expense from receipt'),
                'amount': float(data.get('amount', 0)) if data.get('amount') else 0.0,
                'category': data.get('category', 'Medical'),
                'expense_date': self._validate_date(data.get('expense_date')),
                'vendor': data.get('vendor', ''),
                'confidence_notes': data.get('confidence_notes', '')
            }
            
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e}")
            print(f"Raw response: {response_text}")
            # Return default values if parsing fails
            return self._get_default_fields()
        except Exception as e:
            print(f"Response parsing error: {e}")
            return self._get_default_fields()
    
    def _validate_date(self, date_str):
        """
        Validate and format date string
        """
        if not date_str:
            return datetime.now().strftime('%Y-%m-%d')
        
        try:
            # Try to parse various date formats
            date_formats = ['%Y-%m-%d', '%m/%d/%Y', '%m-%d-%Y', '%d/%m/%Y']
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # If no format works, return today's date
            return datetime.now().strftime('%Y-%m-%d')
            
        except Exception:
            return datetime.now().strftime('%Y-%m-%d')
    
    def _get_default_fields(self):
        """Return default field structure"""
        return {
            'title': 'Medical Expense',
            'description': 'Medical expense from receipt',
            'amount': 0.0,
            'category': 'Medical',
            'expense_date': datetime.now().strftime('%Y-%m-%d'),
            'vendor': '',
            'confidence_notes': 'Failed to parse receipt data'
        }
