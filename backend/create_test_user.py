#!/usr/bin/env python3

import requests

def create_test_user():
    """Create a test user for testing uploads"""
    
    signup_url = "http://localhost:8088/api/auth/signup"
    signup_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "Test",
        "last_name": "Upload"
    }
    
    print("Creating test user...")
    signup_response = requests.post(signup_url, json=signup_data)
    print(f"Signup response: {signup_response.status_code}")
    print(f"Signup response text: {signup_response.text}")
    
    if signup_response.status_code == 201:
        print("✅ Test user created successfully!")
        
        # Now try to login
        login_url = "http://localhost:8088/api/auth/login"
        login_data = {
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        print("\nTesting login...")
        login_response = requests.post(login_url, json=login_data)
        print(f"Login response: {login_response.status_code}")
        print(f"Login response text: {login_response.text}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            print(f"✅ Login successful! Token: {token[:50] if token else 'None'}...")
            return token
        else:
            print("❌ Login failed!")
            return None
    else:
        print("❌ User creation failed!")
        return None

if __name__ == "__main__":
    create_test_user()
