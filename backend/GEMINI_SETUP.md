# Gemini AI Integration Setup

## Overview
The Benefit Vault app now uses Google's Gemini 1.5 Flash multimodal AI for intelligent receipt processing. This provides much more accurate extraction of vendor names, amounts, dates, and categories compared to traditional OCR.

## Setup Instructions

### 1. Get Your Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### 2. Add API Key to Environment
1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and add your Gemini API key:
   ```
   GEMINI_API_KEY=your-actual-api-key-here
   ```

### 3. How It Works
- **Primary**: Gemini 1.5 Flash processes receipt images using multimodal AI
- **Fallback**: If <PERSON> fails, pytesseract OCR is used as backup
- **Smart Extraction**: <PERSON> understands context and can identify:
  - Vendor/Provider names
  - Total amounts (even with complex receipt layouts)
  - Transaction dates
  - Medical expense categories (Medical, Pharmacy, Dental, Vision, etc.)
  - Descriptive titles and descriptions

### 4. Categories Supported
- **Medical**: General medical services, doctor visits, hospital bills
- **Pharmacy**: Prescription medications, over-the-counter medicines  
- **Dental**: Dental services, orthodontics
- **Vision**: Eye exams, glasses, contacts
- **Mental Health**: Therapy, counseling, psychiatric services
- **Alternative Medicine**: Chiropractic, acupuncture, massage therapy

### 5. Testing
Upload a receipt image and check the backend logs to see:
- Raw OCR text extraction
- Field-by-field parsing results
- Confidence levels and any parsing notes

## Troubleshooting
- If Gemini fails to initialize, the app will fall back to pytesseract OCR
- Check backend logs for detailed processing information
- Ensure your API key is valid and has sufficient quota
