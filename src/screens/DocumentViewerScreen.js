import React, { useState, useCallback, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  StatusBar,
  Dimensions,
  ActivityIndicator,
  Platform,
  Share,
  TouchableWithoutFeedback,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  Easing,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

// Modern color palette with vibrant accents
const COLORS = {
  background: '#0A0A12', // Dark background for better contrast
  backgroundLight: '#1C1C23',
  text: '#FFFFFF',
  textSecondary: '#A0A0B0',
  primary: '#5E7BEA', // Softer blue
  primaryLight: 'rgba(94, 123, 234, 0.1)',
  danger: '#FF5A5F', // Softer red
  success: '#4CD964',
  surface: 'rgba(30, 30, 40, 0.85)', // Darker semi-transparent surface
  surfaceLight: 'rgba(255, 255, 255, 0.08)',
  border: 'rgba(255, 255, 255, 0.1)',
  shadow: 'rgba(0, 0, 0, 0.3)',
};

const SPRING_CONFIG = {
  damping: 15,
  mass: 1,
  stiffness: 150,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 2,
};

const DocumentViewerScreen = ({ route, navigation }) => {
  const { uri, title = 'Document' } = route.params;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [controlsVisible, setControlsVisible] = useState(true);
  const insets = useSafeAreaInsets();

  // Animation values
  const scale = useSharedValue(1);
  const focalX = useSharedValue(0);
  const focalY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);
  const headerTranslateY = useSharedValue(0);
  const controlsOpacity = useSharedValue(1);
  const controlsTranslateY = useSharedValue(0);

  // Toggle controls visibility
  const toggleControls = useCallback(() => {
    setControlsVisible(prev => {
      const newValue = !prev;
      headerOpacity.value = withTiming(newValue ? 1 : 0, { duration: 200 });
      headerTranslateY.value = withTiming(newValue ? 0 : -100, { duration: 250 });
      controlsOpacity.value = withTiming(newValue ? 1 : 0, { duration: 200 });
      controlsTranslateY.value = withTiming(newValue ? 0 : 100, { duration: 250 });
      return newValue;
    });
  }, []);

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (!loading && !error) {
      const timer = setTimeout(() => {
        setControlsVisible(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [loading, error]);

  // Pinch to zoom gesture handler with spring physics
  const pinchHandler = useAnimatedGestureHandler({
    onStart: (_, ctx) => {
      ctx.scale = scale.value;
    },
    onActive: (event, ctx) => {
      scale.value = Math.min(Math.max(1, ctx.scale * event.scale), 4);
      focalX.value = event.focalX;
      focalY.value = event.focalY;
    },
    onEnd: () => {
      if (scale.value < 1.1) {
        scale.value = withSpring(1, SPRING_CONFIG);
      }
    },
  });

  // Animated styles
  const animatedImageStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: withSpring(focalX.value, SPRING_CONFIG) },
      { translateY: withSpring(focalY.value, SPRING_CONFIG) },
      { scale: withSpring(scale.value, SPRING_CONFIG) },
      { translateX: withSpring(-focalX.value, SPRING_CONFIG) },
      { translateY: withSpring(-focalY.value, SPRING_CONFIG) },
    ],
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const controlsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: controlsOpacity.value,
    transform: [{ translateY: controlsTranslateY.value }],
  }));

  const handleShare = useCallback(async () => {
    try {
      await Share.share({
        title: 'Check out this document',
        url: uri,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share document.');
    }
  }, [uri]);

  const handleRetry = useCallback(() => {
    setLoading(true);
    setError(false);
  }, []);

  // Render the header with animations
  const renderHeader = () => (
    <Animated.View style={[styles.header, headerAnimatedStyle]}>
      <BlurView 
        intensity={100} 
        tint="dark" 
        style={styles.headerBlur}
      >
        <View style={[styles.headerContent, { paddingTop: insets.top }]}>
          <TouchableOpacity 
            style={styles.headerButton} 
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle} numberOfLines={1}>
              {title}
            </Text>
            {!loading && !error && (
              <Text style={styles.pageInfo}>
                {Math.round(scale.value * 100)}%
              </Text>
            )}
          </View>
          
          <View style={styles.headerActions}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.shareButton]}
              onPress={handleShare}
              activeOpacity={0.7}
            >
              <Ionicons name="share-outline" size={20} color={COLORS.text} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, styles.moreButton]}
              activeOpacity={0.7}
            >
              <Ionicons name="ellipsis-vertical" size={20} color={COLORS.text} />
            </TouchableOpacity>
          </View>
        </View>
      </BlurView>
    </Animated.View>
  );

  // Render bottom controls
  const renderControls = () => (
    <Animated.View style={[styles.controls, controlsAnimatedStyle]}>
      <BlurView 
        intensity={100} 
        tint="dark" 
        style={styles.controlsBlur}
      >
        <View style={styles.controlsContent}>
          <TouchableOpacity 
            style={[styles.controlButton, { backgroundColor: COLORS.primaryLight }]}
            activeOpacity={0.7}
          >
            <Ionicons name="download-outline" size={22} color={COLORS.primary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.controlButton, { backgroundColor: COLORS.primaryLight }]}
            activeOpacity={0.7}
          >
            <Ionicons name="print-outline" size={22} color={COLORS.primary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.controlButton, { backgroundColor: COLORS.primaryLight }]}
            activeOpacity={0.7}
          >
            <Ionicons name="bookmark-outline" size={22} color={COLORS.primary} />
          </TouchableOpacity>
        </View>
      </BlurView>
    </Animated.View>
  );

  return (
    <TouchableWithoutFeedback onPress={toggleControls}>
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
        
        <PinchGestureHandler
          onGestureEvent={pinchHandler}
          onHandlerStateChange={({ nativeEvent }) => {
            if (nativeEvent.state === State.END && scale.value < 1.1) {
              scale.value = withSpring(1, SPRING_CONFIG);
            }
          }}
        >
          <Animated.View style={styles.viewerContainer}>
            <Animated.Image
              source={{ uri }}
              style={[styles.documentImage, animatedImageStyle]}
              resizeMode="contain"
              onLoadStart={() => setLoading(true)}
              onLoad={() => setLoading(false)}
              onError={() => {
                setLoading(false);
                setError(true);
              }}
            />
          </Animated.View>
        </PinchGestureHandler>

        {loading && (
          <View style={styles.overlay}>
            <View style={styles.loadingContent}>
              <ActivityIndicator size="large" color={COLORS.primary} />
              <Text style={styles.overlayText}>Loading document...</Text>
            </View>
          </View>
        )}

        {error && (
          <View style={styles.overlay}>
            <View style={styles.errorContent}>
              <View style={styles.errorIcon}>
                <Ionicons name="alert-circle" size={48} color={COLORS.danger} />
              </View>
              <Text style={styles.overlayTitle}>Unable to Load</Text>
              <Text style={styles.overlayText}>There was an error loading the document.</Text>
              <TouchableOpacity 
                style={styles.retryButton} 
                onPress={handleRetry}
                activeOpacity={0.8}
              >
                <Text style={styles.retryButtonText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        
        {!loading && !error && controlsVisible && (
          <Animated.View style={[styles.zoomHintContainer, controlsAnimatedStyle]}>
            <View style={styles.zoomHintContent}>
              <Ionicons name="scan-outline" size={16} color={COLORS.textSecondary} />
              <Text style={styles.zoomHintText}>Pinch to zoom • Double tap to reset</Text>
            </View>
          </Animated.View>
        )}
        
        {renderHeader()}
        {!loading && !error && controlsVisible && renderControls()}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  viewerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundLight,
  },
  documentImage: {
    width: width,
    height: height,
  },
  
  // Header Styles
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerBlur: {
    paddingBottom: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 10,
  },
  headerTitle: {
    color: COLORS.text,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  pageInfo: {
    color: COLORS.textSecondary,
    fontSize: 12,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  shareButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  moreButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  
  // Controls Styles
  controls: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    zIndex: 10,
  },
  controlsBlur: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  controlsContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  
  // Overlay & Loading States
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  loadingContent: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 16,
    backgroundColor: COLORS.surface,
    margin: 20,
  },
  errorContent: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 16,
    backgroundColor: COLORS.surface,
    margin: 20,
    maxWidth: '80%',
  },
  errorIcon: {
    backgroundColor: 'rgba(255, 90, 95, 0.1)',
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  overlayTitle: {
    color: COLORS.text,
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  overlayText: {
    color: COLORS.textSecondary,
    fontSize: 15,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 12,
    minWidth: 150,
  },
  retryButtonText: {
    color: COLORS.text,
    fontWeight: '600',
    fontSize: 16,
    textAlign: 'center',
  },
  
  // Zoom Hint
  zoomHintContainer: {
    position: 'absolute',
    bottom: 100,
    alignSelf: 'center',
    zIndex: 5,
  },
  zoomHintContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  zoomHintText: {
    color: COLORS.textSecondary,
    fontSize: 13,
    fontWeight: '500',
    marginLeft: 8,
    letterSpacing: 0.3,
  },
});

export default DocumentViewerScreen;
