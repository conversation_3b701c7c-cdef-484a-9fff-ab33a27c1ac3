#!/usr/bin/env python3

import requests
from io import Bytes<PERSON>

def test_hsa_upload_direct():
    """Test HSA document upload with a known token"""
    
    # Use the token from the logs (you'll need to get a fresh one from the app)
    # For now, let's try to get it by testing different passwords
    
    passwords_to_try = ["password123", "test123", "123456", "password", "test"]
    
    for password in passwords_to_try:
        print(f"Trying password: {password}")
        
        login_url = "http://localhost:8088/api/auth/login"
        login_data = {
            "email": "<EMAIL>",
            "password": password
        }
        
        login_response = requests.post(login_url, json=login_data)
        print(f"Login response: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print(f"✅ Login successful with password: {password}")
            login_result = login_response.json()
            token = login_result.get('access_token')
            
            if token:
                print(f"Token: {token[:50]}...")
                
                # Now test the upload
                test_upload_with_token(token)
                return
        else:
            print(f"❌ Login failed: {login_response.text}")
    
    print("❌ Could not find valid password")

def test_upload_with_token(token):
    """Test upload with a valid token"""
    
    # Create a test image file
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    # Prepare upload data
    upload_url = "http://localhost:8088/api/hsa/documents"
    
    files = {
        'file': ('test_receipt.png', BytesIO(test_image_data), 'image/png')
    }
    
    data = {
        'title': 'Test Medical Receipt',
        'description': 'Test upload for medical expense',
        'amount': '150.00',
        'category': 'Medical',
        'expense_date': '2025-06-08'
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    print("\nUploading HSA document...")
    upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
    print(f"Upload response: {upload_response.status_code}")
    print(f"Upload response text: {upload_response.text}")
    
    if upload_response.status_code == 201:
        print("✅ Upload successful!")
    else:
        print("❌ Upload failed!")

if __name__ == "__main__":
    test_hsa_upload_direct()
