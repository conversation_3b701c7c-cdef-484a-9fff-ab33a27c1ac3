"""
Fixed version of the monthly chart data function
to be copied into app.py
"""

@app.route('/api/hsa/monthly-chart-data', methods=['GET'])
@supabase_auth_required
def get_monthly_chart_data():
    """Get monthly expenses and contributions data for the past 12 months"""
    try:
        user_id = request.current_user_id
        print(f"Getting monthly chart data for user: {user_id}")
        
        from datetime import datetime, timedelta
        import calendar
        
        # Calculate date range for the last 12 months
        current_date = datetime.now()
        start_date = current_date - timedelta(days=365)  # Approximately 12 months ago
        
        # Get all HSA documents for this user
        documents_endpoint = f"{supabase.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&select=*"
        documents_response = requests.get(documents_endpoint, headers=supabase.service_headers)
        
        if documents_response.status_code != 200:
            print(f"Error getting HSA documents: {documents_response.text}")
            return jsonify([])
        
        documents = documents_response.json()
        print(f"Found {len(documents)} HSA documents")
        
        # Get all HSA contributions for this user using the existing method
        print(f"Fetching contributions for user: {user_id}")
        # Use the existing method that's known to work for other parts of the app
        contributions = supabase.get_hsa_contributions(user_id)
        print(f"Found {len(contributions)} HSA contributions")
        # Debug: Print first few contributions
        for i, contrib in enumerate(contributions[:3]):
            print(f"Contribution {i}: {contrib}")
        
        # Process expenses by month
        expenses_by_month = {}
        for doc in documents:
            if doc.get('expense_date') and doc.get('amount'):
                try:
                    expense_date = datetime.strptime(doc['expense_date'], '%Y-%m-%d')
                    if expense_date >= start_date:
                        month_key = expense_date.strftime('%Y-%m')
                        if month_key not in expenses_by_month:
                            expenses_by_month[month_key] = 0
                        expenses_by_month[month_key] += float(doc['amount'])
                except (ValueError, TypeError) as e:
                    print(f"Error parsing expense date {doc.get('expense_date')}: {e}")
                    continue
        
        # Process contributions by month
        contributions_by_month = {}
        print("Processing contributions by month...")
        for contrib in contributions:
            if contrib.get('contribution_date') and contrib.get('amount'):
                try:
                    print(f"Processing contribution: date={contrib['contribution_date']}, amount={contrib['amount']}")
                    contrib_date = datetime.strptime(contrib['contribution_date'], '%Y-%m-%d')
                    if contrib_date >= start_date:
                        month_key = contrib_date.strftime('%Y-%m')
                        if month_key not in contributions_by_month:
                            contributions_by_month[month_key] = 0
                        contributions_by_month[month_key] += float(contrib['amount'])
                        print(f"Added contribution to month {month_key}: ${float(contrib['amount'])}")
                except (ValueError, TypeError) as e:
                    print(f"Error parsing contribution date {contrib.get('contribution_date')}: {e}")
                    continue
        
        # Debug: Print all months with contributions
        print("Months with contributions:")
        for month, amount in contributions_by_month.items():
            print(f"  {month}: ${amount}")
        
        # Generate the last 12 months of data
        monthly_data = []
        print("Generating monthly data...")
        for i in range(11, -1, -1):  # Last 12 months
            month_date = current_date - timedelta(days=30 * i)
            month_key = month_date.strftime('%Y-%m')
            month_name = calendar.month_abbr[month_date.month]
            
            expenses = expenses_by_month.get(month_key, 0)
            contributions = contributions_by_month.get(month_key, 0)
            
            print(f"Month {month_name} ({month_key}): expenses=${expenses}, contributions=${contributions}")
            
            monthly_data.append({
                'month': month_name,
                'month_key': month_key,
                'expenses': expenses,
                'contributions': contributions
            })
        
        print(f"Generated monthly data for {len(monthly_data)} months")
        return jsonify(monthly_data)
        
    except Exception as e:
        print(f"Error in get_monthly_chart_data: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify([]), 500  # Return empty array with 500 status code
