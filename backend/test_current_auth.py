#!/usr/bin/env python3

import requests

def test_current_auth():
    """Test current authentication and document loading"""
    
    # First, login to get a fresh token
    login_url = "http://localhost:8088/api/auth/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "testtest"
    }
    
    print("=== TESTING LOGIN ===")
    login_response = requests.post(login_url, json=login_data)
    print(f"Login status: {login_response.status_code}")
    print(f"Login response: {login_response.text}")
    
    if login_response.status_code == 200:
        login_result = login_response.json()
        token = login_result.get('access_token')
        user_id = login_result.get('user', {}).get('id')
        
        print(f"\n=== TESTING HSA DOCUMENTS ===")
        print(f"User ID: {user_id}")
        print(f"Token: {token[:50]}...")
        
        # Now try to get HSA documents
        docs_url = "http://localhost:8088/api/hsa/documents"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        docs_response = requests.get(docs_url, headers=headers)
        print(f"Documents status: {docs_response.status_code}")
        print(f"Documents response: {docs_response.text}")
        
        if docs_response.status_code == 200:
            print("✅ Authentication and document loading working!")
        else:
            print("❌ Document loading failed!")
    else:
        print("❌ Login failed!")

if __name__ == "__main__":
    test_current_auth()
