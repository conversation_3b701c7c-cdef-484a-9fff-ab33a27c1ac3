import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Image,
  Dimensions,
  StatusBar,
  Platform,
  Share,
  Animated
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import hsaDocumentService from '../services/hsaDocumentService';
import { BlurView } from 'expo-blur';

const { width, height } = Dimensions.get('window');
const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

// Modern color palette
const COLORS = {
  primary: '#0EA5E9', // Sky blue
  primaryDark: '#0284C7', // Darker sky blue
  secondary: '#22C55E', // Green
  secondaryDark: '#16A34A', // Darker green
  danger: '#EF4444', // Red
  dangerDark: '#DC2626', // Darker red
  warning: '#F59E0B', // Amber
  warningDark: '#D97706', // Darker amber
  background: '#F0F9FF', // Very light blue
  surface: '#FFFFFF',
  surfaceAlt: '#F8FAFC', // Light gray-blue
  text: '#0F172A', // Very dark blue-gray
  textSecondary: '#64748B', // Blue-gray
  border: '#E2E8F0', // Light gray-blue
};

const HSADocumentDetailScreen = ({ navigation, route }) => {
  const { logout } = useSupabaseAuth();
  const { documentId } = route.params;
  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const scrollY = useRef(new Animated.Value(0)).current;
  
  // Animation values
  const headerHeight = scrollY.interpolate({
    inputRange: [0, 120],
    outputRange: [240, 100],
    extrapolate: 'clamp'
  });
  
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 60, 90],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp'
  });
  
  const imageScale = scrollY.interpolate({
    inputRange: [-100, 0],
    outputRange: [1.2, 1],
    extrapolate: 'clamp'
  });
  
  const titleTranslateY = scrollY.interpolate({
    inputRange: [0, 120],
    outputRange: [0, -50],
    extrapolate: 'clamp'
  });
  
  const titleScale = scrollY.interpolate({
    inputRange: [0, 120],
    outputRange: [1, 0.8],
    extrapolate: 'clamp'
  });

  useEffect(() => {
    loadDocument();
  }, [documentId]);

  const loadDocument = async () => {
    try {
      setLoading(true);
      const result = await hsaDocumentService.getDocument(documentId);
      if (result.success) {
        setDocument(result.data);
      } else {
        console.error('Failed to load document:', result.error);
        if (result.error && result.error.includes('session has expired')) {
          Alert.alert(
            'Session Error',
            'Unable to load document. Please try again or sign out manually if the issue persists.',
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert('Error', 'Failed to load document details');
        }
      }
    } catch (error) {
      console.error('Error loading document:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatAmount = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return [COLORS.secondary, COLORS.secondaryDark];
      case 'rejected':
        return [COLORS.danger, COLORS.dangerDark];
      case 'pending':
      default:
        return [COLORS.warning, COLORS.warningDark];
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'checkmark-circle';
      case 'rejected':
        return 'close-circle';
      case 'pending':
      default:
        return 'time';
    }
  };

  const handleDelete = async () => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await hsaDocumentService.deleteDocument(documentId);
              if (result.success) {
                Alert.alert('Success', 'Document deleted successfully', [
                  { text: 'OK', onPress: () => navigation.goBack() }
                ]);
              } else {
                Alert.alert('Error', result.error || 'Failed to delete document');
              }
            } catch (error) {
              console.error('Error deleting document:', error);
              Alert.alert('Error', 'An unexpected error occurred');
            }
          }
        }
      ]
    );
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out this HSA document: ${document.title} - ${formatAmount(document.amount)}`,
        url: document.file_url
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share document');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={[COLORS.primary, COLORS.primaryDark]}
          style={styles.loadingContainer}
        >
          <View style={styles.loadingContent}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>Loading document...</Text>
          </View>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (!document) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <View style={styles.errorIconContainer}>
            <Ionicons name="document-outline" size={80} color={COLORS.primary} />
          </View>
          <Text style={styles.errorTitle}>Document Not Found</Text>
          <Text style={styles.errorSubtitle}>The document you're looking for doesn't exist or has been removed.</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <LinearGradient
              colors={[COLORS.primary, COLORS.primaryDark]}
              style={styles.backButtonGradient}
            >
              <Text style={styles.backButtonText}>Go Back</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const statusColors = getStatusColor(document.status);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.primary} />
      
      {/* Animated Header */}
      <Animated.View style={[styles.animatedHeader, { height: headerHeight }]}>
        <AnimatedLinearGradient
          colors={[COLORS.primary, COLORS.primaryDark]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        />
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButtonSmall} 
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          
          <Animated.View style={{
            transform: [
              { translateY: titleTranslateY },
              { scale: titleScale }
            ]
          }}>
            <Text style={styles.headerTitle} numberOfLines={1}>
              {document.title || 'Untitled Document'}
            </Text>
          </Animated.View>
          
          <Animated.View style={[styles.headerAmountContainer, { opacity: headerOpacity }]}>
            <Text style={styles.headerAmount}>{formatAmount(document.amount)}</Text>
          </Animated.View>
        </View>
      </Animated.View>
      
      <Animated.ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
      >
        <View style={styles.scrollViewContent}>
          {/* Status Badge */}
          <View style={styles.statusContainer}>
            <LinearGradient
              colors={statusColors}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.statusBadge}
            >
              <Ionicons 
                name={getStatusIcon(document.status)} 
                size={18} 
                color="#fff" 
                style={styles.statusIcon}
              />
              <Text style={styles.statusText}>
                {document.status?.toUpperCase() || 'PENDING'}
              </Text>
            </LinearGradient>
          </View>

          {/* Document Image */}
          {document.file_url && (
            <View style={styles.imageContainer}>
              <TouchableOpacity 
                style={styles.imageWrapper}
                onPress={() => navigation.navigate('DocumentViewerScreen', { uri: document.file_url })}
                activeOpacity={0.9}
              >
                <Animated.Image 
                  source={{ uri: document.file_url }}
                  style={[styles.documentImage, { transform: [{ scale: imageScale }] }]}
                  resizeMode="cover"
                />
                <View style={styles.imageOverlay}>
                  <BlurView intensity={15} style={styles.blurOverlay}>
                    <Ionicons name="expand-outline" size={24} color="#fff" />
                    <Text style={styles.viewFullText}>View Full</Text>
                  </BlurView>
                </View>
              </TouchableOpacity>
            </View>
          )}

          {/* Amount Card */}
          <View style={styles.amountCard}>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Total Amount</Text>
              <Text style={styles.amountValue}>{formatAmount(document.amount)}</Text>
            </View>
          </View>

          {/* Details Card */}
          <View style={styles.detailsCard}>
            <View style={styles.cardHeader}>
              <Ionicons name="document-text-outline" size={22} color={COLORS.primary} style={styles.cardHeaderIcon} />
              <Text style={styles.cardHeaderTitle}>Document Details</Text>
            </View>
            
            <View style={styles.detailsGrid}>
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="pricetag-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Category</Text>
                  <Text style={styles.detailValue}>{document.category || 'N/A'}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="calendar-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Expense Date</Text>
                  <Text style={styles.detailValue}>{formatDate(document.expense_date)}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="cloud-upload-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Upload Date</Text>
                  <Text style={styles.detailValue}>{formatDate(document.created_at)}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="document-text-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>File Name</Text>
                  <Text style={styles.detailValue}>{document.file_name || 'N/A'}</Text>
                </View>
              </View>

              {document.file_size && (
                <View style={styles.detailRow}>
                  <View style={styles.detailIconContainer}>
                    <Ionicons name="archive-outline" size={20} color={COLORS.primary} />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={styles.detailLabel}>File Size</Text>
                    <Text style={styles.detailValue}>
                      {(document.file_size / 1024).toFixed(1)} KB
                    </Text>
                  </View>
                </View>
              )}
            </View>
          </View>

          {/* Description */}
          {document.description && (
            <View style={styles.descriptionCard}>
              <View style={styles.cardHeader}>
                <Ionicons name="chatbubble-ellipses-outline" size={22} color={COLORS.primary} style={styles.cardHeaderIcon} />
                <Text style={styles.cardHeaderTitle}>Description</Text>
              </View>
              <View style={styles.descriptionBox}>
                <Text style={styles.descriptionText}>{document.description}</Text>
              </View>
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.actionContainer}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => navigation.navigate('HSADocuments', { editDocument: document })}
              activeOpacity={0.8}
            >
              <View style={[styles.actionButtonInner, styles.editButton]}>
                <Ionicons name="pencil-outline" size={22} color={COLORS.primary} />
                <Text style={[styles.buttonText, styles.editButtonText]}>Edit</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleShare}
              activeOpacity={0.8}
            >
              <View style={[styles.actionButtonInner, styles.shareButton]}>
                <Ionicons name="share-social-outline" size={22} color={COLORS.secondary} />
                <Text style={[styles.buttonText, styles.shareButtonText]}>Share</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleDelete}
              activeOpacity={0.8}
            >
              <View style={[styles.actionButtonInner, styles.deleteButton]}>
                <Ionicons name="trash-outline" size={22} color={COLORS.danger} />
                <Text style={[styles.buttonText, styles.deleteButtonText]}>Delete</Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.bottomPadding} />
        </View>
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingTop: 240, // Match initial header height
  },
  animatedHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    overflow: 'hidden',
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
    justifyContent: 'flex-end',
  },
  backButtonSmall: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 10 : 20,
    left: 15,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 10,
    textShadowColor: 'rgba(0,0,0,0.15)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  headerAmountContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  headerAmount: {
    fontSize: 36,
    fontWeight: '800',
    color: '#fff',
    letterSpacing: -0.5,
    textShadowColor: 'rgba(0,0,0,0.2)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.15)',
    padding: 25,
    borderRadius: 16,
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 16,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: COLORS.background,
  },
  errorIconContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: `${COLORS.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  errorTitle: {
    fontSize: 26,
    color: COLORS.text,
    fontWeight: '700',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  errorSubtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginBottom: 36,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: '85%',
  },
  backButton: {
    borderRadius: 30,
    overflow: 'hidden',
    shadowColor: COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  backButtonGradient: {
    paddingHorizontal: 36,
    paddingVertical: 15,
    borderRadius: 30,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  statusIcon: {
    marginRight: 8,
  },
  statusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '700',
    letterSpacing: 0.7,
  },
  imageContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  imageWrapper: {
    position: 'relative',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  documentImage: {
    width: '100%',
    height: 300,
    borderRadius: 20,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    margin: 15,
  },
  blurOverlay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  viewFullText: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 5,
  },
  amountCard: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: 'rgba(0, 0, 0, 0.06)',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.8,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  amountRow: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: 8,
  },
  amountValue: {
    fontSize: 32,
    fontWeight: '700',
    color: COLORS.primary,
  },
  detailsCard: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: 'rgba(0, 0, 0, 0.06)',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.8,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  cardHeader: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    paddingBottom: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardHeaderIcon: {
    marginRight: 10,
  },
  cardHeaderTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text,
  },
  detailsGrid: {
    marginBottom: 10,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  detailIconContainer: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    backgroundColor: `${COLORS.primary}10`,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  descriptionCard: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: 'rgba(0, 0, 0, 0.06)',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.8,
    shadowRadius: 5,
    elevation: 3,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  descriptionBox: {
    padding: 5,
  },
  descriptionText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 24,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginVertical: 10,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  actionButtonInner: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
  },
  editButton: {
    backgroundColor: `${COLORS.primary}10`,
    borderColor: COLORS.primary,
  },
  shareButton: {
    backgroundColor: `${COLORS.secondary}10`,
    borderColor: COLORS.secondary,
  },
  deleteButton: {
    backgroundColor: `${COLORS.danger}10`,
    borderColor: COLORS.danger,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  editButtonText: {
    color: COLORS.primary,
  },
  shareButtonText: {
    color: COLORS.secondary,
  },
  deleteButtonText: {
    color: COLORS.danger,
  },
  bottomPadding: {
    height: 40,
  },
});

export default HSADocumentDetailScreen;
