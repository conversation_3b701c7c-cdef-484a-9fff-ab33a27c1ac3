import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { Text, TouchableOpacity, View, StyleSheet, Dimensions, Animated } from "react-native";
import { BarChart } from "react-native-gifted-charts";
import { SymbolView } from "expo-symbols";
import SegmentedControl from "@react-native-segmented-control/segmented-control";
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import transactionService from "../services/transactionService";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Enum equivalent in JavaScript
const Period = {
  week: "week",
  year: "year",
};

// Format bar value for the label shown above each bar. Values ≥ 1000 are abbreviated, e.g., 1.25k.
const formatBarLabel = (value) => {
  if (value >= 1000) {
    // Keep up to two decimals, strip trailing zeros
    const formatted = (value / 1000)
      .toFixed(2)
      .replace(/\.0+$|0+$/,'');
    return `$${formatted}k`;
  }
  return `$${value}`;
};

const HSAChart = forwardRef(({ data, loading, defaultPeriod = Period.week, initialWeek = null }, ref) => {
  // Initialize all state values
  const [chartPeriod, setChartPeriod] = useState(defaultPeriod || Period.week);
  const [barData, setBarData] = useState([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [chartKey, setChartKey] = useState(0);
  const [transactionType, setTransactionType] = useState("Expense");
  const [totalAmount, setTotalAmount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const totalAmountAnim = useRef(new Animated.Value(0)).current;
  const [displayedTotal, setDisplayedTotal] = useState(0);

  // Store current state values in refs to avoid accessing state during render
  const transactionTypeRef = useRef(transactionType);

  // Update refs when state changes
  useEffect(() => {
    transactionTypeRef.current = transactionType;
  }, [transactionType]);

  // Update displayed total when totalAmount changes
  useEffect(() => {
    totalAmountAnim.setValue(totalAmount);
    setDisplayedTotal(totalAmount);
  }, [totalAmount]);

  // Simple animation for chart transitions
  const animateChartTransition = (newData, newTotal) => {
    setBarData(newData);
    setTotalAmount(newTotal);

    // Animate total amount
    Animated.timing(totalAmountAnim, {
      toValue: newTotal,
      duration: 400,
      useNativeDriver: false,
    }).start();

    // Increment chart key to trigger re-render
    setChartKey(prev => prev + 1);
  };

  // Main data loading function
  const loadData = async (period, date, type) => {
    setIsLoading(true);

    try {
      let fetchedData;
      let processedData;
      let totalValue = 0;

      if (period === Period.week) {
        const { startDate, endDate } = getWeekRange(date);
        fetchedData = await transactionService.getWeeklyChartData(startDate, endDate, type);
        processedData = processWeeklyData(fetchedData, type, date);

        // Calculate total for the week
        if (fetchedData && fetchedData.length > 0) {
          totalValue = fetchedData.reduce((sum, item) => sum + (item.total || 0), 0);
        }

      } else if (period === Period.year) {
        const year = date.getFullYear();
        fetchedData = await transactionService.getYearlyChartData(year, type);
        processedData = processYearlyData(fetchedData, type);
        totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
      }

      // Update chart with new data
      animateChartTransition(processedData, totalValue);

    } catch (error) {
      console.error('Error loading data:', error);
      setBarData([]);
      setTotalAmount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data when period, date, or type changes
  useEffect(() => {
    if (chartPeriod && transactionType && currentDate) {
      loadData(chartPeriod, currentDate, transactionType);
    }
  }, [chartPeriod, currentDate, transactionType]);

  // Initialize with current date on mount
  useEffect(() => {
    const today = new Date();
    setCurrentDate(today);
    setSelectedYear(today.getFullYear());
  }, []);

  // Update current date when selected year changes
  useEffect(() => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(selectedYear);
    setCurrentDate(newDate);
  }, [selectedYear]);

  const getWeekRange = (date) => {
    // Create a new date object to avoid modifying the original
    const currentDate = new Date(date);

    // Get the current day of the week (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeek = currentDate.getDay();

    // Calculate the date of the Sunday that starts this week
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - dayOfWeek);
    startOfWeek.setHours(0, 0, 0, 0); // Set to beginning of the day

    // Calculate the date of the Saturday that ends this week
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999); // Set to end of the day

    return {
      startDate: startOfWeek.getTime(),
      endDate: endOfWeek.getTime(),
    };
  };

  const handlePrevious = () => {
    const newDate = new Date(currentDate);

    if (chartPeriod === Period.week) {
      // Go to previous week (7 days back)
      newDate.setDate(currentDate.getDate() - 7);
      setCurrentDate(newDate);
    } else if (chartPeriod === Period.year) {
      // Go to previous year
      const prevYear = selectedYear - 1;
      setSelectedYear(prevYear);
    }
  };

  const handleNext = () => {
    const newDate = new Date(currentDate);
    const today = new Date();

    if (chartPeriod === Period.week) {
      // Go to next week (7 days forward)
      newDate.setDate(currentDate.getDate() + 7);

      // Don't allow navigating to future weeks beyond current week
      const currentWeekRange = getWeekRange(today);
      const nextWeekRange = getWeekRange(newDate);

      if (nextWeekRange.startDate <= currentWeekRange.startDate) {
        setCurrentDate(newDate);
      }
    } else if (chartPeriod === Period.year) {
      // Go to next year
      const nextYear = selectedYear + 1;

      // Don't allow navigating to future years beyond current year
      if (nextYear <= today.getFullYear()) {
        setSelectedYear(nextYear);
      }
    }
  };

  // Handle swipe gestures
  const handleSwipeGesture = (event) => {
    const { translationX, translationY, velocityX, velocityY, state } = event.nativeEvent;
    
    if (state === State.END) {
      // Check if this is primarily a horizontal gesture
      const isHorizontalGesture = Math.abs(translationX) > Math.abs(translationY);
      const isSignificantHorizontalMovement = Math.abs(translationX) > 50;
      const isSignificantHorizontalVelocity = Math.abs(velocityX) > 500;
      
      // Only trigger navigation if it's clearly a horizontal swipe
      if (isHorizontalGesture && (isSignificantHorizontalMovement || isSignificantHorizontalVelocity)) {
        if (translationX > 0) {
          // Swipe right - go to previous period
          handlePrevious();
        } else {
          // Swipe left - go to next period
          handleNext();
        }
      }
    }
  };

  const processWeeklyData = (data, type, date) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Get the week range to display actual dates
    const weekRange = getWeekRange(date);
    const weekStart = new Date(weekRange.startDate);

    // Create default data structure with all days initialized to 0
    const defaultData = days.map((day, index) => {
      const roundedValue = 0;

      // Calculate the actual date for this day of the week
      const dayDate = new Date(weekStart);
      dayDate.setDate(weekStart.getDate() + index);

      return {
        value: roundedValue,
        label: day,
        frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
        gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
        dayIndex: index,
        dayDate: dayDate,
        topLabelComponent: () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        )
      };
    });

    // If no data is returned, use the default structure
    if (!data || data.length === 0) {
      return defaultData;
    }

    // Fill in actual values where they exist
    data.forEach(item => {
      if (item.dayOfWeek >= 0 && item.dayOfWeek < 7) {
        const roundedValue = Math.round(item.total);
        defaultData[item.dayOfWeek].value = roundedValue;
        defaultData[item.dayOfWeek].topLabelComponent = () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(roundedValue)}</Text>
          </View>
        );
      }
    });

    return defaultData;
  };



  const processYearlyData = (data, type) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Create default data structure with all months initialized to 0
    const defaultData = monthNames.map((month, index) => ({
      value: 0,
      label: month,
      frontColor: type === 'Expense' ? '#ef4444' : '#22c55e',
      gradientColor: type === 'Expense' ? '#dc2626' : '#16a34a',
      topLabelComponent: () => (
        <View style={{ paddingTop: 5, height: 25 }}>
          <Text style={styles.barValueLabel}>{formatBarLabel(0)}</Text>
        </View>
      )
    }));

    // If no data is returned, use the default structure
    if (!data || data.length === 0) {
      return defaultData;
    }

    // Fill in actual values where they exist
    data.forEach(item => {
      const monthIndex = parseInt(item.month) - 1; // Convert month (1-12) to array index (0-11)
      if (monthIndex >= 0 && monthIndex < 12) {
        const value = type === 'Expense' ? (item.expenses || 0) : (item.contributions || 0);
        defaultData[monthIndex].value = value;
        defaultData[monthIndex].topLabelComponent = () => (
          <View style={{ paddingTop: 5, height: 25 }}>
            <Text style={styles.barValueLabel}>{formatBarLabel(value)}</Text>
          </View>
        );
      }
    });

    return defaultData;
  };

  const getPeriodTitle = () => {
    if (chartPeriod === Period.week) {
      // Get the current week's Sunday and Saturday
      const weekRange = getWeekRange(currentDate);
      const startDate = new Date(weekRange.startDate);
      const endDate = new Date(weekRange.endDate);

      // Format dates properly
      const startMonth = startDate.toLocaleDateString("en-US", { month: "short" });
      const endMonth = endDate.toLocaleDateString("en-US", { month: "short" });
      const startDay = startDate.getDate();
      const endDay = endDate.getDate();
      const year = startDate.getFullYear();

      // Handle case where week spans two different months
      if (startMonth === endMonth) {
        return `${startMonth} ${startDay}-${endDay}, ${year}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
      }
    } else if (chartPeriod === Period.year) {
      return selectedYear.toString();
    }
    return "";
  };



  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    setToCurrentWeek: () => {
      setChartPeriod(Period.week);
      const today = new Date();
      setCurrentDate(today);
      setSelectedYear(today.getFullYear());
    }
  }));

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>

        <SegmentedControl
          values={["Week", "Year"]}
          style={styles.segmentedControl}
          selectedIndex={chartPeriod === Period.week ? 0 : 1}
          onChange={(event) => {
            const index = event.nativeEvent.selectedSegmentIndex;
            setChartPeriod(index === 0 ? Period.week : Period.year);
          }}
        />

        <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
        <Text style={styles.periodSubtitle}>
          Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
        </Text>

        <Text style={styles.totalAmount}>$0.00</Text>

        <View style={styles.chartContainer}>
          <View style={styles.loadingOverlay}>
            <Text style={styles.loadingText}>Loading chart data...</Text>
          </View>
        </View>

        <View style={styles.controlsContainer}>
          <TouchableOpacity
            disabled={true}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.left.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"#d1d5db"}
            />
            <Text style={[styles.controlButtonText, {color: '#d1d5db'}]}>Prev {chartPeriod}</Text>
          </TouchableOpacity>

          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />

          <TouchableOpacity
            disabled={true}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.right.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"#d1d5db"}
            />
            <Text style={[styles.controlButtonText, {color: '#d1d5db'}]}>Next {chartPeriod}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Year view with consistent navigation
  if (chartPeriod === Period.year) {
    return (
      <PanGestureHandler
        onGestureEvent={handleSwipeGesture}
        onHandlerStateChange={handleSwipeGesture}
        activeOffsetX={[-20, 20]}
        failOffsetY={[-20, 20]}
        shouldCancelWhenOutside={true}
      >
        <View style={styles.container}>
          <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>

          <SegmentedControl
            values={["Week", "Year"]}
            style={styles.segmentedControl}
            selectedIndex={chartPeriod === Period.week ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setChartPeriod(index === 0 ? Period.week : Period.year);
            }}
          />

          <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
          <Text style={styles.periodSubtitle}>
            Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
          </Text>

          <Animated.Text style={[styles.totalAmount, { opacity: fadeAnim }]}>
            ${displayedTotal.toFixed(2)}
          </Animated.Text>

          <View style={styles.swipeHintContainer}>
            <Text style={styles.swipeHintText}>← Swipe to navigate →</Text>
          </View>

          <View style={styles.chartOuterContainer}>
            <Animated.View
              style={[
                styles.chartContainer,
                {
                  opacity: fadeAnim
                }
              ]}
            >
              <BarChart
                key={chartKey}
                data={barData}
                barWidth={18}
                height={220}
                width={SCREEN_WIDTH - 40}
                minHeight={3}
                barBorderRadius={4}
                showGradient
                spacing={10}
                noOfSections={4}
                yAxisThickness={0}
                xAxisThickness={0}
                xAxisLabelsVerticalShift={5}
                xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
                yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
                horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
                xAxisLabelComponent={(item) => (
                  <Text style={{ color: "gray", fontSize: 12 }}>{item.label}</Text>
                )}
                isAnimated
                animationDuration={600}
                topLabelContainerStyle={{ height: 30 }}
                scrollToEnd={false}
                disableScroll={false}
                leftShiftForLastIndexToFit={true}
                formatYLabel={(value) => value.toLocaleString()}
                maxValue={(() => {
                  const NUMBER_OF_SECTIONS = 4;
                  const maxVal = Math.max(...barData.map(item => item.value), 0);

                  let roundedMax;

                  if (maxVal <= 100) {
                    roundedMax = Math.ceil(maxVal / 25) * 25;
                  } else if (maxVal <= 1000) {
                    roundedMax = Math.ceil(maxVal / 100) * 100;
                  } else if (maxVal <= 10000) {
                    roundedMax = Math.ceil(maxVal / 500) * 500;
                  } else {
                    roundedMax = Math.ceil(maxVal / 1000) * 1000;
                  }

                  const remainder = roundedMax % NUMBER_OF_SECTIONS;
                  if (remainder !== 0) {
                    roundedMax = roundedMax + (NUMBER_OF_SECTIONS - remainder);
                  }

                  const sectionSize = roundedMax / NUMBER_OF_SECTIONS;
                  return roundedMax + sectionSize;
                })()}
              />
            </Animated.View>
          </View>

          <View style={styles.controlsContainer}>
            <TouchableOpacity
              onPress={handlePrevious}
              style={styles.controlButton}
            >
              <SymbolView
                name="chevron.left.circle.fill"
                size={40}
                type="hierarchical"
                tintColor={"gray"}
              />
              <Text style={styles.controlButtonText}>Prev {chartPeriod}</Text>
            </TouchableOpacity>

            <SegmentedControl
              values={["Expense", "Contribution"]}
              style={styles.typeSegmentedControl}
              selectedIndex={transactionType === "Expense" ? 0 : 1}
              onChange={(event) => {
                const index = event.nativeEvent.selectedSegmentIndex;
                setTransactionType(index === 0 ? "Expense" : "Contribution");
              }}
            />

            <TouchableOpacity
              onPress={handleNext}
              style={styles.controlButton}
            >
              <SymbolView
                name="chevron.right.circle.fill"
                size={40}
                type="hierarchical"
                tintColor={"gray"}
              />
              <Text style={styles.controlButtonText}>Next {chartPeriod}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </PanGestureHandler>
    );
  }

  // Week view
  return (
    <PanGestureHandler
      onGestureEvent={handleSwipeGesture}
      onHandlerStateChange={handleSwipeGesture}
      activeOffsetX={[-20, 20]}
      failOffsetY={[-20, 20]}
      shouldCancelWhenOutside={true}
    >
      <View style={styles.container}>
        <Text style={styles.title}>{transactionType === "Expense" ? "HSA Expenses" : "HSA Contributions"}</Text>

        <SegmentedControl
          values={["Week", "Year"]}
          style={styles.segmentedControl}
          selectedIndex={chartPeriod === Period.week ? 0 : 1}
          onChange={(event) => {
            const index = event.nativeEvent.selectedSegmentIndex;
            setChartPeriod(index === 0 ? Period.week : Period.year);
          }}
        />

        <Text style={styles.periodTitle}>{getPeriodTitle()}</Text>
        <Text style={styles.periodSubtitle}>
          Total {transactionType === "Expense" ? "Spending" : "Contributions"}{" "}
        </Text>

        <Animated.Text style={[styles.totalAmount, { opacity: fadeAnim }]}>
          ${displayedTotal.toFixed(2)}
        </Animated.Text>

        <View style={styles.swipeHintContainer}>
          <Text style={styles.swipeHintText}>← Swipe to navigate →</Text>
        </View>

        <View style={styles.chartOuterContainer}>
          <Animated.View
            style={[
              styles.chartContainer,
              {
                opacity: fadeAnim
              }
            ]}
          >
            <BarChart
              key={chartKey}
              data={barData}
              barWidth={24}
              height={220}
              width={SCREEN_WIDTH - 40}
              minHeight={3}
              barBorderRadius={4}
              showGradient
              spacing={18}
              noOfSections={4}
              yAxisThickness={0}
              xAxisThickness={0}
              xAxisLabelsVerticalShift={5}
              xAxisLabelTextStyle={{ color: "gray", fontSize: 12 }}
              yAxisTextStyle={{ color: "gray", fontSize: 10, marginRight: 2 }}
              horizontalRulesStyle={{ width: '98%', marginLeft: '1%' }}
              xAxisLabelComponent={(item) => {
                if (item.dayDate) {
                  return (
                    <View style={{ alignItems: 'center', width: 40, paddingHorizontal: 2 }}>
                      <Text style={{ color: "#666", fontSize: 11, fontWeight: '500' }}>{item.label}</Text>
                      <Text style={{ color: "#666", fontSize: 13, fontWeight: '600', marginTop: 3 }}>{item.dayDate.getDate()}</Text>
                    </View>
                  );
                }
                return <Text style={{ color: "gray", fontSize: 12 }}>{item.label}</Text>;
              }}
              isAnimated
              animationDuration={600}
              topLabelContainerStyle={{ height: 30 }}
              scrollToEnd={false}
              disableScroll={true}
              initialSpacing={16}
              leftShiftForLastIndexToFit={true}
              formatYLabel={(value) => {
                return value.toLocaleString();
              }}
              maxValue={(() => {
                const NUMBER_OF_SECTIONS = 4;
                const maxVal = Math.max(...barData.map(item => item.value), 0);

                let roundedMax;

                if (maxVal <= 100) {
                  roundedMax = Math.ceil(maxVal / 25) * 25;
                } else if (maxVal <= 1000) {
                  roundedMax = Math.ceil(maxVal / 100) * 100;
                } else if (maxVal <= 10000) {
                  roundedMax = Math.ceil(maxVal / 500) * 500;
                } else {
                  roundedMax = Math.ceil(maxVal / 1000) * 1000;
                }

                const remainder = roundedMax % NUMBER_OF_SECTIONS;
                if (remainder !== 0) {
                  roundedMax = roundedMax + (NUMBER_OF_SECTIONS - remainder);
                }

                const sectionSize = roundedMax / NUMBER_OF_SECTIONS;
                return roundedMax + sectionSize;
              })()}
            />
          </Animated.View>
        </View>

        <View style={styles.controlsContainer}>
          <TouchableOpacity
            onPress={handlePrevious}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.left.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"gray"}
            />
            <Text style={styles.controlButtonText}>Prev {chartPeriod}</Text>
          </TouchableOpacity>

          <SegmentedControl
            values={["Expense", "Contribution"]}
            style={styles.typeSegmentedControl}
            selectedIndex={transactionType === "Expense" ? 0 : 1}
            onChange={(event) => {
              const index = event.nativeEvent.selectedSegmentIndex;
              setTransactionType(index === 0 ? "Expense" : "Contribution");
            }}
          />

          <TouchableOpacity
            onPress={handleNext}
            style={styles.controlButton}
          >
            <SymbolView
              name="chevron.right.circle.fill"
              size={40}
              type="hierarchical"
              tintColor={"gray"}
            />
            <Text style={styles.controlButtonText}>Next {chartPeriod}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </PanGestureHandler>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
  },
  segmentedControl: {
    marginBottom: 16,
  },
  periodTitle: {
    fontWeight: "700",
    fontSize: 18,
    marginBottom: 8
  },
  periodSubtitle: {
    color: "gray"
  },
  totalAmount: {
    fontWeight: "700",
    fontSize: 32,
    marginBottom: 16
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "baseline",
    marginTop: 16,
  },
  controlButton: {
    alignItems: "center"
  },
  controlButtonText: {
    fontSize: 11,
    color: "gray"
  },
  typeSegmentedControl: {
    width: 200
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    height: 220,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  barValueLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  swipeHintContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  swipeHintText: {
    fontSize: 14,
    color: 'gray',
  },
  chartContainer: {
    height: 220,
  },
  chartOuterContainer: {
    position: 'relative',
  },

});

export default HSAChart;
