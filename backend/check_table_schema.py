#!/usr/bin/env python3

import requests
import os
from dotenv import load_dotenv

load_dotenv()

def check_table_schemas():
    """Check the actual table schemas in Supabase"""
    
    supabase_url = os.getenv('SUPABASE_URL')
    service_key = os.getenv('SUPABASE_SERVICE_KEY')
    
    headers = {
        'apikey': service_key,
        'Authorization': f'Bearer {service_key}',
        'Content-Type': 'application/json'
    }
    
    # Get profiles table schema
    print("=== PROFILES TABLE SCHEMA ===")
    profiles_url = f"{supabase_url}/rest/v1/profiles?limit=1"
    response = requests.get(profiles_url, headers=headers)
    if response.status_code == 200:
        profiles = response.json()
        if profiles:
            print("Columns in profiles table:")
            for key, value in profiles[0].items():
                print(f"  - {key}: {type(value).__name__} = {value}")
    
    print("\n=== HSA_DOCUMENTS TABLE SCHEMA ===")
    hsa_url = f"{supabase_url}/rest/v1/hsa_documents?limit=1"
    response = requests.get(hsa_url, headers=headers)
    if response.status_code == 200:
        documents = response.json()
        if documents:
            print("Columns in hsa_documents table:")
            for key, value in documents[0].items():
                print(f"  - {key}: {type(value).__name__} = {value}")
    
    print("\n=== RELATIONSHIP DEMONSTRATION ===")
    # Show how they're linked
    profiles_response = requests.get(f"{supabase_url}/rest/v1/profiles", headers=headers)
    hsa_response = requests.get(f"{supabase_url}/rest/v1/hsa_documents", headers=headers)
    
    if profiles_response.status_code == 200 and hsa_response.status_code == 200:
        profiles = profiles_response.json()
        documents = hsa_response.json()
        
        print("Showing relationship between tables:")
        for profile in profiles:
            user_docs = [doc for doc in documents if doc['user_id'] == profile['id']]
            print(f"\nUser: {profile['email']} (ID: {profile['id']})")
            print(f"  Has {len(user_docs)} HSA documents:")
            for doc in user_docs:
                print(f"    - {doc['title']} (Doc ID: {doc['id']})")

if __name__ == "__main__":
    check_table_schemas()
