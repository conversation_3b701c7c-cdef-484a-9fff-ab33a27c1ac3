-- Create HSA contributions table
CREATE TABLE IF NOT EXISTS hsa_contributions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    contribution_date DATE NOT NULL DEFAULT CURRENT_DATE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_hsa_contributions_user_id ON hsa_contributions(user_id);
CREATE INDEX IF NOT EXISTS idx_hsa_contributions_date ON hsa_contributions(contribution_date);

-- Enable Row Level Security
ALTER TABLE hsa_contributions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to only access their own contributions
CREATE POLICY "Users can view their own contributions" ON hsa_contributions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own contributions" ON hsa_contributions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contributions" ON hsa_contributions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contributions" ON hsa_contributions
    FOR DELETE USING (auth.uid() = user_id);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_hsa_contributions_updated_at BEFORE UPDATE
    ON hsa_contributions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
