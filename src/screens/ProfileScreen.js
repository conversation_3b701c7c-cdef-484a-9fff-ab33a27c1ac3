import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity, 
  ScrollView, 
  Image, 
  Alert, 
  ActivityIndicator, 
  TextInput, 
  Modal, 
  Switch,
  Linking
} from 'react-native';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import * as ImagePicker from 'expo-image-picker';
import { MaterialIcons, Ionicons, FontAwesome5 } from '@expo/vector-icons';
import profileService from '../services/profileService';
import ChangePasswordModal from '../components/ChangePasswordModal';

const ProfileScreen = ({ navigation }) => {
  const { user, logout, isAuthenticated } = useSupabaseAuth();
  const [loading, setLoading] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [profilePicture, setProfilePicture] = useState(null);
  const [showImageOptions, setShowImageOptions] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);
  
  // Notification preferences
  const [notificationPreferences, setNotificationPreferences] = useState({
    pushEnabled: true,
    emailEnabled: true,
    marketingEnabled: false,
  });
  
  // Check for authentication issues
  useEffect(() => {
    if (!isAuthenticated) {
      console.log('User not authenticated in ProfileScreen, redirecting to AuthResetScreen');
      navigation.navigate('AuthResetScreen');
    }
  }, [isAuthenticated, navigation]);
  
  // Profile data state
  const [profileData, setProfileData] = useState({
    name: user?.name || 'User',
    email: user?.email || 'No email available',
    phone: '',
    profile_picture_url: null,
  });
  
  // Form data for editing
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: ''
  });
  
  // Load profile data
  useEffect(() => {
    if (isAuthenticated && user) {
      loadProfileData();
    }
  }, [isAuthenticated, user]);
  
  const loadProfileData = async () => {
    if (!user) {
      console.log('Cannot load profile: user is undefined');
      return;
    }
    
    setLoading(true);
    try {
      const data = await profileService.getProfile();
      
      // Handle case when profile data couldn't be fetched (likely auth issue)
      if (!data) {
        console.log('Profile data could not be loaded, possible authentication issue');
        setProfileData({
          name: user?.name || 'User',
          email: user?.email || 'No email available',
          phone: '',
          profile_picture_url: null,
        });
        
        console.log('Profile data could not be loaded, continuing with default values');
        return;
      }
      
      // Process successful profile data
      setProfileData({
        name: data.name || user?.name || 'User',
        email: data.email || user?.email || 'No email available',
        phone: data.phone || '',
        profile_picture_url: data.profile_picture_url,
      });
      
      if (data.profile_picture_url) {
        setProfilePicture(data.profile_picture_url);
      }
      
      // Load notification preferences if available
      if (data.notification_preferences) {
        setNotificationPreferences({
          pushEnabled: data.notification_preferences.push_enabled ?? true,
          emailEnabled: data.notification_preferences.email_enabled ?? true,
          marketingEnabled: data.notification_preferences.marketing_enabled ?? false,
        });
      }
    } catch (error) {
      console.error('Error loading profile data:', error);
      // If we get a 401 error, redirect to auth reset
      if (error.response && error.response.status === 401) {
        navigation.navigate('AuthResetScreen');
      }
    } finally {
      setLoading(false);
    }
  };
  
  const handleStartEdit = () => {
    setFormData({
      name: profileData.name,
      email: profileData.email,
      phone: profileData.phone || ''
    });
    setIsEditing(true);
  };
  
  const handleCancelEdit = () => {
    setIsEditing(false);
  };
  
  const handleSaveProfile = async () => {
    try {
      setIsSaving(true);
      
      // Validate form data
      if (!formData.name.trim()) {
        Alert.alert('Error', 'Name cannot be empty');
        setIsSaving(false);
        return;
      }
      
      if (!formData.email.trim() || !formData.email.includes('@')) {
        Alert.alert('Error', 'Please enter a valid email address');
        setIsSaving(false);
        return;
      }
      
      // Prepare update data
      const updateData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        profile_picture_url: profilePicture,
        notification_preferences: {
          push_enabled: notificationPreferences.pushEnabled,
          email_enabled: notificationPreferences.emailEnabled,
          marketing_enabled: notificationPreferences.marketingEnabled
        }
      };
      
      // Call API to update profile
      const result = await profileService.updateProfile(updateData);
      
      // Update local state
      setProfileData({
        ...profileData,
        ...updateData
      });
      
      setIsEditing(false);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const handlePickImage = async () => {
    setShowImageOptions(false);
    
    // Request permission
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'We need camera roll permission to upload a profile picture');
      return;
    }
    
    // Launch image picker
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    });
    
    if (!result.canceled && result.assets && result.assets[0]) {
      // In a real app, you would upload this image to your server/storage
      // For now, we'll just set the local URI
      setProfilePicture(result.assets[0].uri);
    }
  };
  
  const handleTakePhoto = async () => {
    setShowImageOptions(false);
    
    // Request permission
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'We need camera permission to take a profile picture');
      return;
    }
    
    // Launch camera
    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    });
    
    if (!result.canceled && result.assets && result.assets[0]) {
      // In a real app, you would upload this image to your server/storage
      // For now, we'll just set the local URI
      setProfilePicture(result.assets[0].uri);
    }
  };

  const updateNotificationPreference = async (key, value) => {
    try {
      const updatedPreferences = {
        ...notificationPreferences,
        [key]: value
      };
      
      setNotificationPreferences(updatedPreferences);
      
      // Update on server
      const updateData = {
        notification_preferences: {
          push_enabled: updatedPreferences.pushEnabled,
          email_enabled: updatedPreferences.emailEnabled,
          marketing_enabled: updatedPreferences.marketingEnabled
        }
      };
      
      await profileService.updateProfile(updateData);
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      Alert.alert('Error', 'Failed to update notification settings');
      
      // Revert the change on error
      setNotificationPreferences({...notificationPreferences});
    }
  };
  
  const handleDeleteAccount = () => {
    setShowDeleteAccountModal(false);
    
    // Show confirmation alert
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted. Are you sure?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              // Call API to delete account
              await profileService.deleteAccount();
              
              // Log out after successful deletion
              await logout();
              
              // Navigation to login will happen automatically due to auth state change
            } catch (error) {
              console.error('Error deleting account:', error);
              Alert.alert('Error', 'Failed to delete account. Please try again.');
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // Show loading indicator if we're still checking authentication
  if (!isAuthenticated) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color="#4a6fa5" />
        <Text style={styles.loadingText}>Checking authentication...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      {/* Header with profile picture and basic info */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.avatarContainer}
          onPress={() => isEditing && setShowImageOptions(true)}
        >
          {profilePicture ? (
            <Image source={{ uri: profilePicture }} style={styles.avatarImage} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>{profileData.name.charAt(0).toUpperCase()}</Text>
            </View>
          )}
          {isEditing && (
            <View style={styles.editAvatarIcon}>
              <MaterialIcons name="edit" size={16} color="#fff" />
            </View>
          )}
        </TouchableOpacity>
        
        {isEditing ? (
          <View style={styles.editForm}>
            <TextInput
              style={styles.input}
              placeholder="Name"
              value={formData.name}
              onChangeText={(text) => setFormData({...formData, name: text})}
            />
            <TextInput
              style={styles.input}
              placeholder="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({...formData, email: text})}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <TextInput
              style={styles.input}
              placeholder="Phone Number"
              value={formData.phone}
              onChangeText={(text) => setFormData({...formData, phone: text})}
              keyboardType="phone-pad"
            />
          </View>
        ) : (
          <>
            <Text style={styles.userName}>{profileData.name}</Text>
            <Text style={styles.userEmail}>{profileData.email}</Text>
            {profileData.phone && <Text style={styles.userPhone}>{profileData.phone}</Text>}
          </>
        )}
      </View>
      
      {/* Image picker modal */}
      <Modal
        visible={showImageOptions}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowImageOptions(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowImageOptions(false)}
        >
          <View style={styles.modalContent}>
            <TouchableOpacity style={styles.modalOption} onPress={handlePickImage}>
              <MaterialIcons name="photo-library" size={24} color="#4a6fa5" />
              <Text style={styles.modalOptionText}>Choose from Gallery</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.modalOption} onPress={handleTakePhoto}>
              <MaterialIcons name="camera-alt" size={24} color="#4a6fa5" />
              <Text style={styles.modalOptionText}>Take Photo</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.modalOption, styles.cancelOption]}
              onPress={() => setShowImageOptions(false)}
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Account Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account Settings</Text>
        
        {isEditing ? (
          <View style={styles.editButtonsContainer}>
            <TouchableOpacity 
              style={[styles.editButton, styles.saveButton]} 
              onPress={handleSaveProfile}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.editButtonText}>Save Changes</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.editButton, styles.cancelButton]} 
              onPress={handleCancelEdit}
              disabled={isSaving}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity style={styles.settingItem} onPress={handleStartEdit}>
            <View style={styles.settingRow}>
              <Ionicons name="person-outline" size={22} color="#4a6fa5" />
              <Text style={styles.settingText}>Edit Profile</Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#ccc" />
          </TouchableOpacity>
        )}

        <TouchableOpacity 
          style={styles.settingItem} 
          onPress={() => setShowChangePasswordModal(true)}
        >
          <View style={styles.settingRow}>
            <Ionicons name="lock-closed-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Change Password</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => navigation.navigate('HSADocuments')}
        >
          <View style={styles.settingRow}>
            <Ionicons name="document-text-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>My Documents</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => navigation.navigate('HSAContributionHistory')}
        >
          <View style={styles.settingRow}>
            <Ionicons name="calendar-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Contribution History</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
      </View>

      {/* Notification Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Preferences</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingRow}>
            <Ionicons name="notifications-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Push Notifications</Text>
          </View>
          <Switch
            value={notificationPreferences.pushEnabled}
            onValueChange={(value) => updateNotificationPreference('pushEnabled', value)}
            trackColor={{ false: '#d1d1d1', true: '#a3c9f1' }}
            thumbColor={notificationPreferences.pushEnabled ? '#4a6fa5' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingRow}>
            <Ionicons name="mail-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Email Notifications</Text>
          </View>
          <Switch
            value={notificationPreferences.emailEnabled}
            onValueChange={(value) => updateNotificationPreference('emailEnabled', value)}
            trackColor={{ false: '#d1d1d1', true: '#a3c9f1' }}
            thumbColor={notificationPreferences.emailEnabled ? '#4a6fa5' : '#f4f3f4'}
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingRow}>
            <Ionicons name="megaphone-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Marketing Communications</Text>
          </View>
          <Switch
            value={notificationPreferences.marketingEnabled}
            onValueChange={(value) => updateNotificationPreference('marketingEnabled', value)}
            trackColor={{ false: '#d1d1d1', true: '#a3c9f1' }}
            thumbColor={notificationPreferences.marketingEnabled ? '#4a6fa5' : '#f4f3f4'}
          />
        </View>
      </View>

      {/* Help & Support */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Help & Support</Text>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => Linking.openURL('https://benefitvault.app/faq')}
        >
          <View style={styles.settingRow}>
            <Ionicons name="help-circle-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>FAQ</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => Linking.openURL('https://benefitvault.app/contact')}
        >
          <View style={styles.settingRow}>
            <Ionicons name="chatbubble-ellipses-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Contact Support</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => Linking.openURL('https://benefitvault.app/privacy')}
        >
          <View style={styles.settingRow}>
            <Ionicons name="shield-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Privacy Policy</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.settingItem}
          onPress={() => Linking.openURL('https://benefitvault.app/terms')}
        >
          <View style={styles.settingRow}>
            <Ionicons name="document-outline" size={22} color="#4a6fa5" />
            <Text style={styles.settingText}>Terms of Service</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#ccc" />
        </TouchableOpacity>
      </View>

      {/* Debug and Account Actions */}
      <View style={styles.section}>
        <TouchableOpacity 
          style={[styles.settingItem, styles.dangerItem]}
          onPress={() => setShowDeleteAccountModal(true)}
        >
          <View style={styles.settingRow}>
            <Ionicons name="trash-outline" size={22} color="#e53935" />
            <Text style={styles.dangerText}>Delete Account</Text>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.logoutButton}
          onPress={async () => {
            try {
              setIsLoggingOut(true);
              const result = await logout();
              // Successful logout will automatically redirect to login screen
              // due to the conditional rendering in AppNavigator
              if (!result.success) {
                Alert.alert('Error', result.message);
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to log out');
              console.error(error);
            } finally {
              setIsLoggingOut(false);
            }
          }}
          disabled={isLoggingOut}
        >
          {isLoggingOut ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name="log-out-outline" size={20} color="#fff" style={styles.logoutIcon} />
              <Text style={styles.logoutButtonText}>Log Out</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      <ChangePasswordModal
        visible={showChangePasswordModal}
        onClose={() => setShowChangePasswordModal(false)}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4a6fa5',
  },
  header: {
    backgroundColor: '#4a6fa5',
    padding: 20,
    alignItems: 'center',
    paddingBottom: 30,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 3,
    borderColor: '#ffffff',
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
    backgroundColor: '#e1e8f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
  },
  avatarText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: '#4a6fa5',
  },
  editAvatarIcon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#4a6fa5',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 5,
  },
  userPhone: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  editForm: {
    width: '100%',
    marginTop: 10,
  },
  section: {
    backgroundColor: '#fff',
    marginVertical: 10,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  dangerItem: {
    borderBottomWidth: 0,
  },
  dangerText: {
    fontSize: 16,
    color: '#e53935',
    marginLeft: 12,
  },
  logoutButton: {
    marginTop: 16,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    backgroundColor: '#4a6fa5',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  logoutIcon: {
    marginRight: 8,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
  },
  editButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  editButton: {
    flex: 1,
    padding: 14,
    borderRadius: 10,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  saveButton: {
    backgroundColor: '#4a6fa5',
  },
  cancelButton: {
    backgroundColor: '#f1f1f1',
  },
  editButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButtonText: {
    color: '#757575',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalOptionText: {
    fontSize: 16,
    marginLeft: 15,
    color: '#333',
  },
  cancelOption: {
    justifyContent: 'center',
    marginTop: 5,
    borderBottomWidth: 0,
  },
  cancelText: {
    fontSize: 16,
    color: '#e53935',
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default ProfileScreen;
