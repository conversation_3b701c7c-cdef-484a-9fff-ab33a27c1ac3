{"name": "benefit-vault", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-segmented-control/segmented-control": "^2.5.7", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@shopify/react-native-skia": "^2.0.1", "axios": "^1.9.0", "expo": "~53.0.9", "expo-blur": "^14.1.5", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-font": "^13.3.1", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-secure-store": "~14.2.3", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "^0.4.5", "react": "19.0.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.25.0", "react-native-gifted-charts": "^1.4.61", "react-native-graph": "^1.1.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}